<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="mini" inline>
      <el-form-item v-for="item in questions" :key="item.prop" :prop="item.prop" :label="item.label">
        <el-checkbox-group v-model="form[item.prop]" size="small" @change="handleChange(item.prop)">
          <el-checkbox v-for="option in item.options" :key="option" :label="option" border></el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "AN0053",
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != "") {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      questions: [
        { label: "1、地板环境（多选）", prop: "p1", options: ['地板状况良好', '地板防滑', '地板有固定的防滑垫', '不存在以上情况'], multiple: true},
        { label: "2、灯光环境（多选）", prop: "p2", options: ['灯的亮度可看清物品', '床上开关灯方便', '晚上路边及楼道灯照明良好', '不存在以上情况'], multiple: true},
        { label: "3、楼梯环境（多选）", prop: "p3", options: ['室内楼梯旁有可用的扶手', '室外楼梯旁有可用的扶手', '室内外的楼梯使用安全方便', '楼梯的边缘清晰', '不存在以上情况'], multiple: true},
        { label: "4、休息环境（多选）", prop: "p4", options: ['上下床方便安全', '能从躺椅上方便安全站起来', '不存在以上情况'], multiple: true},
        { label: "5、洗浴环境（多选）", prop: "p5", options: ['进出厕所方便安全', '进出浴室方便安全', '进出淋浴通道方便安全', '淋室和浴池均有扶手', '有固定的防滑垫', '厕所接近浴室', '不存在以上情况'], multiple: true},
        { label: "6、其他居家环境（多选）", prop: "p6", options: ['使用入口门安全方便', '通道无杂乱物品', '房屋周围路况良好', '能方便安全地将饭菜从厨房拿到餐桌上', '不失衡状态下可轻松拿到常用物品', '不存在以上情况'], multiple: true},
        { label: "7、躯体功能", prop: "p7", options: ['使用入口门安全方便', '穿舒适防滑的鞋', '能照看宠物(无跌倒风险时)', '不存在以上情况'], multiple: true},
      ],
      form: {
        p1: [],
        p2: [],
        p3: [],
        p4: [],
        p5: [],
        p6: [],
      },
      // 表单校验
      rules: {
        p1: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p2: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p3: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p4: [
          {
            required: true,
            message: "请选择",
          },
        ],
        p5: [
          {
            required: true,
            message: "请选择",
          },
        ],
        p6: [
          {
            required: true,
            message: "请选择",
          },
        ],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      console.log(this.form)
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
    handleChange(val) {
      const existOptions = ['不存在以上情况'];

      // 获取最后选择的选项
      const lastSelected = this.form[val][this.form[val].length - 1];

      // 如果最后选择的选项在 existOptions 中
      if (existOptions.includes(lastSelected)) {
        // 只保留这个选项
        this.form[val] = [lastSelected];
      } else {
        // 如果选择的是其他选项，移除所有在 existOptions 中的选项
        this.form[val] = this.form[val].filter(item => !existOptions.includes(item));
      }
    }
  },
};
</script>
<style scoped>
:deep(.el-checkbox--mini.is-bordered) {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

:deep(.el-checkbox__input) {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

:deep(.el-checkbox__label) {
  padding-left: 10px;
}

:deep(.el-form-item) {
  width: 100%;
}

:deep(.el-form-item__label) {
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

:deep(.el-form-item__content) {
  width: 100%;
}

:deep(.el-checkbox-group) {
  font-size: 0;
  margin-top: 7px;
}

:deep(.el-checkbox) {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
  padding: 8px 15px 5px 10px !important;
}

:deep(.el-checkbox.is-bordered+.el-checkbox.is-bordered) {
  margin-left: 0;
  margin-top: 5px;
}
</style>
