<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" style="overflow-y: auto">
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p1" label="胃蛋白酶原I">
            <el-input v-model="form.p1" @input="handleNumberInput($event, 'p1')">
              <template slot="append">ug/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p2" label="胃蛋白酶原Ⅱ">
            <el-input v-model="form.p2" @input="handleNumberInput($event, 'p2')">
              <template slot="append">ug/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p3" label="胃蛋白酶原I/Ⅱ">
            <el-input v-model="form.p3" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0065',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
        p2: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '胃蛋白酶原I必须输入', trigger: 'blur' }],
        p2: [{ required: true, message: '胃蛋白酶原Ⅱ必须输入', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleNumberInput(event, field) {
      let value = event.replace(/[^0-9.]/g, '');

      // 处理多个小数点的情况
      const decimalParts = value.split('.');
      if (decimalParts.length > 2) {
        value = decimalParts[0] + '.' + decimalParts.slice(1).join('');
      }

      // 限制整数部分为3位
      if (decimalParts[0].length > 3) {
        value = decimalParts[0].substring(0, 3) + (decimalParts[1] ? '.' + decimalParts[1] : '');
      }

      // 限制小数部分为2位
      if (decimalParts.length > 1 && decimalParts[1].length > 2) {
        value = decimalParts[0] + '.' + decimalParts[1].substring(0, 2);
      }

      this.form[field] = value;
      if (this.form.p1 && this.form.p2) {
        this.form.p3 = (this.form.p1 / this.form.p2).toFixed(2);
      } else {
        this.form.p3 = undefined;
      }
    },
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
