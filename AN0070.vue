<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" size="mini">
      <el-form-item prop="p1" label="1、您是否存在结直肠癌家族史（您的父母、子女、兄弟姐妹是否存在结直肠癌病史）？">
        <el-radio-group v-model="form.p1">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p2" label="2、您是否存在慢性便秘（近2年来便秘每年在2个月以上）？">
        <el-radio-group v-model="form.p2">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p3" label="3、您是否存在慢性腹泻（近2年来腹泻累计持续超过3个月，每次发作持续时间在1周以上）？">
        <el-radio-group v-model="form.p3">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p4" label="4、你是否存在黏液血便？">
        <el-radio-group v-model="form.p4">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p5" label="5、你是否存在不良生活事件史（发生在近20年内，并在事件发生后对调查对象造成较大精神创伤或痛苦）？">
        <el-radio-group v-model="form.p5">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p6" label="6、你是否存在慢性阑尾炎或阑尾切除史？">
        <el-radio-group v-model="form.p6">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p7" label="7、你是否存在慢性胆道疾病史或胆囊切除史？">
        <el-radio-group v-model="form.p7">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0070',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
        p2: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '是否存在结直肠癌家族史必须选择', trigger: 'blur' }],
        p2: [{ required: true, message: '是否存在慢性便秘必须选择', trigger: 'blur' }],
        p3: [{ required: true, message: '是否存在慢性腹泻必须选择', trigger: 'blur' }],
        p4: [{ required: true, message: '是否存在黏液血便必须选择', trigger: 'blur' }],
        p5: [{ required: true, message: '是否存在不良生活事件史必须选择', trigger: 'blur' }],
        p6: [{ required: true, message: '是否存在慢性阑尾炎或阑尾切除史必须选择', trigger: 'blur' }],
        p7: [{ required: true, message: '是否存在慢性胆道疾病史或胆囊切除史必须选择', trigger: 'blur' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>

<style scoped>
/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item__label {
  /* text-align: right; */
  vertical-align: middle;
  /* float: left; */
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>
