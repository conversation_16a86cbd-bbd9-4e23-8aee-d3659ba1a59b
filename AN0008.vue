<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" style="overflow-y: auto">
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p1" label="PSA">
            <el-input v-model="form.p1" @input="handleNumberInput">
              <template slot="append">ng/ml</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0008',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: 'PSA必须输入', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleNumberInput(event) {
      let value = event.replace(/[^0-9.]/g, '');

      // 处理多个小数点的情况
      const decimalParts = value.split('.');
      if (decimalParts.length > 2) {
        value = decimalParts[0] + '.' + decimalParts.slice(1).join('');
      }

      // 限制整数部分为3位
      if (decimalParts[0].length > 3) {
        value = decimalParts[0].substring(0, 3) + (decimalParts[1] ? '.' + decimalParts[1] : '');
      }

      // 限制小数部分为2位
      if (decimalParts.length > 1 && decimalParts[1].length > 2) {
        value = decimalParts[0] + '.' + decimalParts[1].substring(0, 2);
      }

      this.form.p1 = value;
    },
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
