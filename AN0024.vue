<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="left"
            label-width="auto"
            size="mini"
        >
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item
                        label="红细胞:"
                        prop="p1"
                        class="inline-form-item"
                    >
                        <el-input-number
                            class="input-unit"
                            :controls="false"
                            style="width: 120px"
                            v-model="form.p1"
                            :precision="2"
                            :max="999.99"
                            :min="0"
                        ></el-input-number>
                        <div class="unit">10¹²/L</div>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item
                        label="血红蛋白:"
                        prop="p2"
                        class="inline-form-item"
                    >
                        <el-input-number
                            class="input-unit"
                            :controls="false"
                            style="width: 120px"
                            v-model="form.p2"
                            :precision="2"
                            :max="999.99"
                            :min="0"
                        ></el-input-number>
                        <div class="unit">g/L</div>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item
                        label="白细胞:"
                        prop="p3"
                        class="inline-form-item"
                    >
                        <el-input-number
                            class="input-unit"
                            :controls="false"
                            style="width: 120px"
                            v-model="form.p3"
                            :precision="2"
                            :max="999.99"
                            :min="0"
                        ></el-input-number>
                        <div class="unit">10⁹/L</div>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item
                        label="血小板:"
                        prop="p4"
                        class="inline-form-item"
                    >
                        <el-input-number
                            class="input-unit"
                            :controls="false"
                            style="width: 120px"
                            v-model="form.p4"
                            :precision="2"
                            :max="999.99"
                            :min="0"
                        ></el-input-number>
                        <div class="unit">10⁹/L</div>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item
                        label="HCT:"
                        prop="p5"
                        class="inline-form-item"
                    >
                        <el-input-number
                            class="input-unit"
                            :controls="false"
                            style="width: 120px"
                            v-model="form.p5"
                            :precision="0"
                            :max="100"
                            :min="0"
                        ></el-input-number>
                        <div class="unit">%</div>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>
<script>
export default {
    name: "AN0024",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p1: undefined,
                p2: undefined,
                p3: undefined,
                p4: undefined,
                p5: undefined,
            },
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: false,
                        message: "请输入红细胞",
                        trigger: "blur",
                    },
                ],
                p2: [
                    {
                        required: false,
                        message: "请输入血红蛋白",
                        trigger: "blur",
                    },
                ],
                p3: [
                    {
                        required: false,
                        message: "请输入白细胞",
                        trigger: "blur",
                    },
                ],
                p4: [
                    {
                        required: false,
                        message: "请输入血小板",
                        trigger: "blur",
                    },
                ],
                p5: [
                    {
                        required: false,
                        message: "请输入HCT",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
<style scoped>
.inline-form-item ::v-deep(.el-form-item__content) {
    display: flex;
    align-items: center;
    flex-direction: row !important;
}

.input-unit ::v-deep .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.input-item ::v-deep .el-form-item__content {
    display: flex;
}
/deep/ .el-form-item__content.el-form-item__content {
    gap: 0px !important;
}
/deep/ .el-form-item.el-form-item {
    flex-direction: row !important;
    margin-bottom: 20px !important;
}
.unit {
    height: 28px;
    line-height: 28px;
    box-sizing: border-box;
    background-color: #f5f7fa;
    color: #909399;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    white-space: nowrap;
    width: 50px;
    text-align: center;
    font-size: 12px;
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
</style>
