<template>
    <div>
      <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini" style="overflow-y: auto">
        <el-form-item prop="p1" label="超声所见">
          <el-input type="textarea" v-model="form.p1" :rows="5" maxlength="1000" show-word-limit></el-input>
        </el-form-item>
        <el-form-item prop="p2" label="超声提示">
          <el-input type="textarea" v-model="form.p2" :rows="5" maxlength="1000" show-word-limit></el-input>
        </el-form-item>
        <el-form-item prop="p3" label="肾脏占位情况（可多选）">
            <el-checkbox-group v-model="form.p3" @change="handleChange">
                <el-checkbox label="可疑占位" value="可疑占位" border></el-checkbox>
                <el-checkbox label="可疑结节" value="可疑结节" border></el-checkbox>
                <el-checkbox label="低回声结节" value="低回声结节" border></el-checkbox>
                <el-checkbox label="中低回声结节" value="中低回声结节" border></el-checkbox>
                <el-checkbox label="混合性回声结节" value="混合性回声结节" border></el-checkbox>
                <el-checkbox label="无明显占位或结节" value="无明显占位或结节" border></el-checkbox>
            </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
  </template>
  
  <script>
  export default {
    name: 'AN0055',
    props: {
      formData: String,
    },
    components: {},
    created() {
      if (this.formData != null && this.formData != '') {
        this.form = JSON.parse(this.formData);
      }
    },
    data() {
      return {
        form: {
          p1: undefined,
          p3: []
        },
        // 表单校验
        rules: {
          p1: [{ required: true, message: '超声所见必须输入', trigger: 'blur' }],
          p2: [{ required: true, message: '超声提示必须输入', trigger: 'blur' }],
          p3: [{ required: true, message: '肾脏占位情况必须选择', trigger: 'blur' }],
        },
      };
    },
    methods: {
      handleChange(e) {
        if (e.length > 1 && e[0] == '无明显占位或结节') {
          this.form.p3 = e.splice(1)
        } else if (e.length > 1 && e.indexOf('无明显占位或结节') != -1) {
          this.form.p3 = ['无明显占位或结节']
        }
      },
      /** 表单值 */
      getData() {
        return this.form;
      },
      validateData() {
        let rs = true;
        this.$refs['form'].validate((valid) => {
          if (valid) {
            rs = false;
          }
        });
        return rs;
      },
    },
  };
  </script>
  <style scoped>
  /deep/.el-checkbox--mini.is-bordered {
    padding: 6px 15px 0 15px !important;
    border-radius: 3px;
    height: 28px;
  }
  
  /deep/.el-checkbox__input {
    display: none !important;
    white-space: nowrap;
    cursor: pointer;
    outline: 0;
    line-height: 1;
    vertical-align: middle;
  }
  
  /deep/.el-checkbox__label {
    padding-left: 0px;
  }
  
  /deep/.el-form-item__label {
    text-align: right;
    vertical-align: middle;
    /* float: left; */
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    box-sizing: border-box;
    font-weight: 450;
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
    color: #000000;
  }
  
  /deep/.el-checkbox-group {
    font-size: 0;
    margin-top: 7px;
  }
  
  /deep/.el-checkbox {
    color: #000000;
    font-weight: 400;
    line-height: 1;
    cursor: pointer;
    white-space: nowrap;
    outline: 0;
    margin-right: 10px;
    margin-bottom: 10px;
  }
  </style>