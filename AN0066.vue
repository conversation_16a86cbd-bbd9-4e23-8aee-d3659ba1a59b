<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" style="overflow-y: auto">
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p1" label="胃泌素17">
            <el-input v-model="form.p1" @input="handleNumberInput">
              <template slot="append">pmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0066',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '胃泌素17必须输入', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleNumberInput(event) {
      // 必须位非负整数
      let value = event.replace(/[^0-9]/g, '');

      // 处理负数的情况
      if (value.startsWith('-')) {
        value = '-' + value.substring(1);
      }

      // 限制整数部分为3位
      if (value.length > 3) {
        value = value.substring(0, 3)
      }

      this.form.p1 = value;
    },
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
