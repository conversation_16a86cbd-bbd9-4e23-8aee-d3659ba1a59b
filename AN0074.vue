<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini" style="overflow-y: auto">
      <el-form-item prop="p1" label="1、您是否存在宫颈癌家族史（您的父母、子女、兄弟姐妹是否存在宫颈癌病史）？">
        <el-radio-group v-model="form.p1">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p2" label="2、您的长期居住地址">
        <el-select v-model="p2[0]" placeholder="省/自治区/直辖市" style="width: 140px;margin-right: 20px" @change="handleP2Select($event, 0)">
          <el-option v-for="(op, index) in cityOptions[0].children" :key="index" :label="op.name" :value="op.name"></el-option>
        </el-select>
        <el-select v-model="p2[1]" placeholder="省/自治区/直辖市" style="width: 140px;margin-right: 20px" @change="handleP2Select($event, 1)">
          <el-option v-for="(op, index) in currentCities" :key="index" :label="op.name" :value="op.name"></el-option>
        </el-select>
        <el-select v-model="p2[2]" placeholder="省/自治区/直辖市" style="width: 140px" @change="handleP2Select($event, 2)">
          <el-option v-for="(op, index) in currentDistricts" :key="index" :label="op.name" :value="op.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="p3" label="3、您是否患有食管癌前疾病（巴雷特食管、食管白斑症、食管憩室、贲门失弛缓症、食管管型、返流性食管炎、食管良性狭窄）或癌前病变（低级别上皮内瘤变、高级别上皮内瘤变）？">
        <el-radio-group v-model="form.p3">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p4" label="4、你是否存在进行性吞咽困难的症状？">
        <el-radio-group v-model="form.p4">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0074',
  props: {
    formData: String,
  },
  components: {},
  computed: {
    currentCities() {
      if (!this.p2[0]) return [];
      const prov = this.cityOptions[0].children.find(p => p.name === this.p2[0]);
      return prov ? prov.children : [];
    },
    currentDistricts() {
      if (!this.p2[1]) return [];
      const city = this.currentCities.find(c => c.name === this.p2[1]);
      return city ? city.children : [];
    }
  },
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
    let citys = this.cityStr.split(';').map(item => item.split(','));
    for (let i = 0;i < citys.length;i++) {
      this.check_city(['中国', ...citys[i]], this.cityOptions);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
      },
      p2: ['', '', ''],
      cityStr: '安徽,宿州市,埇桥区;安徽,宿州市,灵璧县;安徽,亳州市,蒙城县;安徽,淮南市,寿县;安徽,滁州市,定远县;安徽,合肥市,长丰县;安徽,阜阳市,太和县;安徽,阜阳市,颍东区;安徽,阜阳市,颍州区;安徽,滁州市,天长市;安徽,合肥市,肥东县;安徽,合肥市,-;安徽,合肥市,肥西县;安徽,合肥市,巢湖市;安徽,六安市,金寨县;安徽,马鞍山市,-;安徽,马鞍山市,当涂县;安徽,合肥市,庐江县;安徽,芜湖市,-;安徽,铜陵市,-;安徽,合肥市,-;安徽,蚌埠市,-;安徽,铜陵市,枞阳县;安徽,阜阳市,-;福建,厦门市,-;福建,龙岩市,武平县;福建,莆田市,莆田县;甘肃,平凉市,静宁县;甘肃,甘南州,临潭县;甘肃,兰州市,城关区;甘肃,兰州市,七里河区;甘肃,兰州市,西固区;甘肃,白银市,景泰县;广东,韶关市,南雄市;广东,揭阳市,揭西县;广东,梅州市,梅县区;广东,汕尾市,陆丰市;广东,汕尾市,海丰县;广东,汕尾市,-;广东,汕头市,南澳县;广东,揭阳市,-;广东,潮州市,-;广东,汕头市,-;河北,邯郸市,大名县;河北,邯郸市,磁县;河北,邯郸市,武安市;河北,承德市,丰宁满族自治县;河北,石家庄市,-;河北,承德市,-;河北,邢台市,-;河北,沧州市,-;河北,保定市,涿州市;河南,商丘市,虞城县;河南,周口市,郸城县;河南,商丘市,睢县;河南,周口市,沈丘县;河南,濮阳市,濮阳县;河南,濮阳市,华龙区;河南,开封市,祥符区;河南,漯河市,召陵区;河南,漯河市,郾城区;河南,漯河市,源汇区;河南,驻马店市,西平县;河南,鹤壁市,-;河南,信阳市,罗山县;河南,新乡市,辉县市;河南,许昌市,禹州市;河南,安阳市,林州市;河南,信阳市,浉河区;河南,郑州市,巩义市;河南,三门峡市,湖滨区;河南,洛阳市,洛宁县;河南,洛阳市,栾川县;河南,洛阳市,嵩县;河南,洛阳市,新安县;河南,南阳市,内乡县;河南,洛阳市,伊川县;河南,洛阳市,-;河南,洛阳市,孟津县;河南,洛阳市,汝阳县;河南,济源市,-;河南,许昌市,-;河南,安阳市,-;河南,商丘市,-;河南,安阳市,滑县;湖北,黄冈市,麻城市;湖北,十堰市,郧阳区;湖北,襄阳市,襄州区;江苏,徐州市,-;江苏,连云港市,东海县;江苏,淮安市,淮阴区;江苏,淮安市,盱眙县;江苏,淮安市,洪泽区;江苏,连云港市,灌云县;江苏,淮安市,清江浦区;江苏,连云港市,赣榆区;江苏,连云港市,灌南县;江苏,淮安市,涟水县;江苏,淮安市,淮安区;江苏,淮安市,金湖县;江苏,扬州市,宝应县;江苏,盐城市,滨海县;江苏,盐城市,阜宁县;江苏,盐城市,建湖县;江苏,盐城市,射阳县;江苏,盐城市,盐都区;江苏,盐城市,亭湖区;江苏,盐城市,大丰区;江苏,盐城市,东台市;江苏,镇江市,扬中市;江苏,镇江市,丹阳市;江苏,泰州市,泰兴市;江苏,南通市,海安市;江苏,常州市,金坛区;江苏,常州市,-;江苏,南通市,如皋市;江苏,常州市,溧阳市;江苏,无锡市,江阴市;江苏,无锡市,宜兴市;江苏,苏州市,张家港市;江苏,连云港市,-;江苏,淮安市,-;江苏,苏州市,张家港市;江苏,泰州市,泰兴市;江苏,南通市,-;江苏,-,各县;江苏,扬州市,高邮市;江苏,盐城市,射阳县;江苏,盐城市,大丰区;内蒙古,锡林郭勒盟,-;内蒙古,锡林郭勒盟,锡林浩特市;内蒙古,锡林郭勒盟,西乌珠穆沁旗;内蒙古,呼伦贝尔市,-;内蒙古,通辽市,-;宁夏,中卫市,中宁县;宁夏,中卫市,-;宁夏,吴忠市,青铜峡市;宁夏,银川市,-;宁夏,银川市,贺兰县;山东,枣庄市,滕州市;山东,菏泽市,单县;山东,济宁市,邹城市;山东,济宁市,曲阜市;山东,菏泽市,巨野县;山东,泰安市,宁阳县;山东,临沂市,莒南县;山东,济宁市,汶上县;山东,临沂市,沂南县;山东,济宁市,梁山县;山东,泰安市,肥城市;山东,淄博市,沂源县;山东,济南市,-;山东,济南市,章丘区;山东,潍坊市,临朐县;山东,淄博市,临淄区;山东,青岛市,黄岛区;山东,潍坊市,高密市;山东,青岛市,-;山东,滨州市,滨城区;山东,济南市,-;山东,济宁市,汶上县;山东,枣庄市,-;山东,济宁市,金乡县;山西,晋城市,阳城县;山西,晋中市,太谷区;山西,晋中市,祁县;山西,长治市,-;山西,晋城市,-;陕西,宝鸡市,金台区;陕西,宝鸡市,千阳县;陕西,宝鸡市,麟游县;陕西,西安市,鄠邑区;陕西,西安市,碑林区;陕西,安康市,宁陕县;陕西,西安市,未央区;陕西,西安市,莲湖区;陕西,西安市,雁塔区;陕西,西安市,高陵区;陕西,铜川市,王益区;陕西,渭南市,临渭区;陕西,渭南市,华州区;陕西,安康市,紫阳县;陕西,安康市,汉滨区;陕西,商洛市,商州区;陕西,安康市,旬阳县;陕西,西安市,-;陕西,商洛市,商南县;四川,泸州市,合江县;四川,宜宾市,长宁县;四川,泸州市,龙马潭区;四川,宜宾市,翠屏区;四川,广元市,朝天区;四川,广元市,旺苍县;四川,广元市,青川县;四川,广元市,剑阁县;四川,南充市,阆中市;四川,绵阳市,盐亭县;四川,绵阳市,游仙区;四川,绵阳市,北川羌族自治县;四川,绵阳市,安州区;四川,达州市,大竹县;四川,南充市,高坪区;四川,绵阳市,三台县;四川,德阳市,罗江区;四川,遂宁市,射洪县;四川,广安市,广安区;四川,德阳市,绵竹市;四川,德阳市,旌阳区;四川,德阳市,什邡市;四川,德阳市,广汉市;四川,成都市,彭州市;四川,成都市,-;四川,阿坝州,汶川县;四川,泸州市,-;四川,成都市,彭州市;新疆,伊犁州,奎屯市;新疆,和田地区,-;云南,红河州,屏边苗族自治县;云南,红河州,蒙自市;云南,红河州,泸西县;云南,红河州,个旧市;云南,红河州,开远市;云南,曲靖市,沾益区;云南,玉溪市,江川区;云南,红河州,石屏县;云南,玉溪市,澄江市;云南,玉溪市,红塔区;云南,玉溪市,峨山彝族自治县;云南,昆明市,官渡区;云南,昆明市,盘龙区;云南,昆明市,西山区;云南,昆明市,东川区;云南,玉溪市,新平彝族傣族自治县;云南,玉溪市,易门县;云南,楚雄州,楚雄市;云南,西双版纳州,景洪市;浙江,台州市,-;重庆,重庆市,酉阳土家族苗族自治县;重庆,重庆市,万盛经开区;重庆,重庆市,武隆区;重庆,重庆市,黔江区;重庆,重庆市,江津区;重庆,重庆市,巴南区;重庆,重庆市,南岸区;重庆,重庆市,九龙坡区;重庆,重庆市,渝中区;重庆,重庆市,城口县;重庆,重庆市,开州区;重庆,重庆市,云阳县;重庆,重庆市,万州区;重庆,重庆市,忠县;重庆,重庆市,潼南区;重庆,重庆市,-',
      cityOptions: [],
      // 表单校验
      rules: {
        p1: [{ required: true, message: '是否存在食管癌家族史必须选择', trigger: 'blur' }],
        p2: [{ required: true, message: '长期居住地址必须选择', validator: (rule, value, callback) => {
          if (!this.form.p2) {
            callback(new Error('长期居住地址必须选择'));
          } else {
            callback();
          }
        }
        }],
        p3: [{ required: true, message: '是否患有食管癌前疾病必须选择', trigger: 'blur' }],
        p4: [{ required: true, message: '是否存在进行性吞咽困难的症状必须选择', trigger: 'blur' }],
      },
    };
  },
  methods: {
    check_city(names, list) {
      // 如果传入的 names 数组为空，直接返回，结束递归
      if (names.length === 0) {
        return;
      }
      const currentName = names[0];
      const remainingNames = names.slice(1);
      let existingItem = list.find(item => item.name === currentName);
      if (!existingItem) {
        existingItem = {
          index: list.length,
          name: currentName,
          children: []
        };
        list.push(existingItem);
      }
      this.check_city(remainingNames, existingItem.children);
    },
    handleP2Select(e, index) {
      if (index == 0) {
        this.p2[1] = '';
        this.p2[2] = '';
      } else if (index == 1) {
        this.p2[2] = '';
      }
      let p2 = []
      for (let i=0;i<this.p2.length;i++) {
        if (this.p2[i] != '') {
          p2.push(this.p2[i]);
        }
      }
      this.form.p2 = ''
      if (p2.length == 3) {
        this.form.p2 = p2.join(',')
        console.log('this.form.p2', this.form.p2);
      }
      this.$refs['form'].validateField('p2', (valid) => {
        if (valid) {
          this.$refs['form'].clearValidate('p2');
        }
      });
    },
    /** 表单值 */
    getData() {
      
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>

<style scoped>
/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item__label {
  /* text-align: right; */
  vertical-align: middle;
  /* float: left; */
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>
