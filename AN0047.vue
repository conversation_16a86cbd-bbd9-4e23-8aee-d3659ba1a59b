<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            label-width="80px"
            style="overflow-y: auto"
            size="mini"
        >
            <el-form-item label="患者是否按计划完成研究？" prop="p1">
                <el-radio-group v-model="form.p1">
                    <el-radio label="是" border>是</el-radio>
                    <el-radio label="否" border>否</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="最后一次用药日期" prop="p2">
                <el-date-picker
                    v-model="form.p2"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                    style="width: 150px"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item label="未完成原因" prop="p3">
                <el-radio-group
                    v-model="form.p3"
                    style="
                        display: flex;
                        flex-direction: column;
                        width: 400px;
                        overflow: hidden;
                    "
                >
                    <el-radio
                        style="margin-left: 10px"
                        class="mb-10"
                        label="患者不能按研究方案进行研究，请在“备注”处说明。"
                        border
                        >患者不能按研究方案进行研究，请在“备注”处说明。</el-radio
                    >
                    <el-radio label="服用禁忌药物" border class="mb-10"
                        >服用禁忌药物</el-radio
                    >
                    <el-radio label="试验过程中怀孕" border class="mb-10"
                        >试验过程中怀孕</el-radio
                    >
                    <el-radio
                        label="不良事件，研究者判定无法继续"
                        border
                        class="mb-10"
                        >不良事件，研究者判定无法继续</el-radio
                    >
                    <el-radio
                        label="伴发其它疾病，研究者判定无法继续,请在“备注”处说明。"
                        class="mb-10"
                        border
                        >伴发其它疾病，研究者判定无法继续,请在“备注”处说明。</el-radio
                    >

                    <el-radio
                        label="经研究者判定，因异常的实验室检测值无法继续试验"
                        class="mb-10"
                        border
                        >经研究者判定，因异常的实验室检测值无法继续试验</el-radio
                    >
                    <el-radio label="研究者判定疗效不佳" border class="mb-10"
                        >研究者判定疗效不佳</el-radio
                    >
                    <el-radio
                        label="疾病痊愈，提前结束治疗"
                        border
                        class="mb-10"
                        >疾病痊愈，提前结束治疗</el-radio
                    >
                    <el-radio
                        label="患者自愿退出研究,请在“备注”处说明。"
                        border
                        class="mb-10"
                        >患者自愿退出研究,请在“备注”处说明。</el-radio
                    >
                    <el-radio label="失访" border class="mb-10">失访</el-radio>
                    <el-radio
                        label="其他,请在“备注”处说明。"
                        border
                        style="margin-right: 30px"
                        >其他,请在“备注”处说明。</el-radio
                    >
                </el-radio-group>
            </el-form-item>

            <el-form-item label="备注" prop="p4">
                <el-input
                    v-model="form.p4"
                    style="width: 600px"
                    type="textarea"
                    maxlength="1000"
                    show-word-limit
                ></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0047",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {},
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "患者是否按计划完成研究？",
                        trigger: "change",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "最后一次用药日期",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
<style>
.mb-10 {
    margin-bottom: 10px;
}
</style>
