<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="mini" inline>
      <el-form-item v-for="item in questions" :key="item.prop" :prop="item.prop" :label="item.label">
        <!-- <el-radio-group v-if="item.options" v-model="form[item.prop]">
          <el-radio v-for="option in item.options" :key="option" :label="option" border />
        </el-radio-group> -->
        <el-checkbox-group v-if="item.options" v-model="form[item.prop]" size="small" @change="handleChange(item.prop)">
          <el-checkbox v-for="option in item.options" :key="option" :label="option" border></el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "AN0052",
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != "") {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      questions: [
        { label: "1、请受试者记住以下5样东西，等一下我会再问您：苹果、钢笔、领带、房子、汽车" },
        { label: "2、给受试者一支笔和一张画有钟面的白纸，请让其画出钟刻数并标出10:50指针所指的位置", prop: "p1", options: ['时钟刻度标记正确', '时间标记正确', '无法正确画出时钟刻数和标记时间']},
        { label: "3、请受试者回答刚才让其记住的5件物品？", prop: "p2", options: ['苹果', '钢笔', '领带', '房子', '汽车', '无法回答/回答全错误']},
        { label: "4、我会跟您讲一个故事，请认真听，讲完后我会问您跟故事有关的问题：小红曾是一名成功的股票经纪人，她在股市挣了很多钱，后来她遇上帅气的小明，与他结婚并有一个孩子，在兰州定居。小红辞职在家孩子，等孩子们长大后，小红又重返工作岗位，从此，他们过着幸福生活。 请问他们住在哪个省?", prop: "p3", options: ['兰州', '无法回答/回答不是兰州']},
      ],
      form: {
        p1: [],
        p2: [],
        p3: [],
      },
      // 表单校验
      rules: {
        p1: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p2: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p3: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ]
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      console.log(this.form)
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
    handleChange(val) {
      const existOptions = ['无法正确画出时钟刻数和标记时间', '无法回答/回答全错误', '无法回答/回答不是兰州'];
      
      // 获取最后选择的选项
      const lastSelected = this.form[val][this.form[val].length - 1];
      
      // 如果最后选择的选项在 existOptions 中
      if (existOptions.includes(lastSelected)) {
        // 只保留这个选项
        this.form[val] = [lastSelected];
      } else {
        // 如果选择的是其他选项，移除所有在 existOptions 中的选项
        this.form[val] = this.form[val].filter(item => !existOptions.includes(item));
      }
    }
  },
};
</script>
<style scoped>
:deep(.el-checkbox--mini.is-bordered) {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

:deep(.el-checkbox__input) {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

:deep(.el-checkbox__label) {
  padding-left: 10px;
}

:deep(.el-form-item) {
  width: 100%;
}

:deep(.el-form-item__label) {
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

:deep(.el-form-item__content) {
  width: 100%;
}

:deep(.el-checkbox-group) {
  font-size: 0;
  margin-top: 7px;
}

:deep(.el-checkbox) {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
  padding: 8px 15px 5px 10px !important;
}

:deep(.el-checkbox.is-bordered+.el-checkbox.is-bordered) {
  margin-left: 0;
  margin-top: 5px;
}
</style>
