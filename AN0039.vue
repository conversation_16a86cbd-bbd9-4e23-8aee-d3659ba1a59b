<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="right"
            label-width="110px"
            size="mini"
        >
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="TJC28:" prop="p1">
                        <el-input-number
                            v-model="form.p1"
                            :precision="0"
                            :step="1"
                            :min="0"
                            :max="28"
                            :controls="false"
                            @change="calculate"
                            style="width: 150px"
                        ></el-input-number>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="SJC28:" prop="p2">
                        <el-input-number
                            v-model="form.p2"
                            :precision="0"
                            :step="1"
                            :min="0"
                            :max="28"
                            :controls="false"
                            @change="calculate"
                            style="width: 150px"
                        ></el-input-number>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="ESR:" prop="p3" class="input-item">
                        <el-input-number
                            class="input-unit"
                            :controls="false"
                            style="width: 120px"
                            v-model="form.p3"
                            :precision="0"
                            :max="1000"
                            :min="0"
                            @change="calculate"
                        ></el-input-number>
                        <div class="unit">mm/h</div>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="PGA:" prop="p4">
                        <el-input-number
                            v-model="form.p4"
                            :precision="0"
                            :step="1"
                            :min="0"
                            :max="100"
                            :controls="false"
                            @change="calculate"
                            style="width: 150px"
                        ></el-input-number>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="DAS28-ESR:" prop="p5">
                        <el-input
                            v-model="form.p5"
                            disabled
                            style="width: 150px"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0039",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {},
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: true,
                        message: "请输入TJC28",
                        trigger: "blur",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "请输入SJC28",
                        trigger: "blur",
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "请输入ESR",
                        trigger: "blur",
                    },
                ],
                p4: [
                    {
                        required: true,
                        message: "请输入PGA",
                        trigger: "blur",
                    },
                ],
                p5: [
                    {
                        required: true,
                        message: "请输入DAS28-ESR",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        calculate() {
            if (this.form.p1 && this.form.p2 && this.form.p3 && this.form.p4) {
                this.form.p5 = (
                    0.56 * this.form.p1 +
                    0.28 * this.form.p2 +
                    0.7 * Math.log(this.form.p3) +
                    0.014 * this.form.p4
                ).toFixed(1);
                console.log(this.form.p5, "========res");
            } else {
                this.form.p5 = "";
            }
        },
    },
};
</script>
<style scoped>
.input-unit ::v-deep .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.unit {
    height: 26px;
    background-color: #f5f7fa;
    color: #909399;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    white-space: nowrap;
    width: 50px;
    text-align: center;
    font-size: 12px;
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-item ::v-deep .el-form-item__content {
    display: flex;
}
</style>
