<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="mini" label-position="top" label-width="80px" style="overflow-y: auto">
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p1" label="肌酐">
            <el-input v-model="form.p1" @input="handleNumberInput($event, 'p1')" @blur="handleBlur('p1')">
              <template slot="append">μmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p2" label="尿素">
            <el-input v-model="form.p2" @input="handleNumberInput($event, 'p2')" @blur="handleBlur('p2')">
              <template slot="append">mmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p3" label="尿酸">
            <el-input v-model="form.p3" @input="handleNumberInput($event, 'p3')" @blur="handleBlur('p3')">
              <template slot="append">μmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p4" label="二氧化碳">
            <el-input v-model="form.p4" @input="handleNumberInput($event, 'p4')" @blur="handleBlur('p4')">
              <template slot="append">mmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p5" label="磷">
            <el-input v-model="form.p5" @input="handleNumberInput($event, 'p5')" @blur="handleBlur('p5')">
              <template slot="append">mmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0093',
  props: {
    formData: String,
    context: {
      type: Object,
      default: () => ({
        person: {}
      })
    }
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
    if (this.context?.person != null && this.context?.person != '') {
      let sexs = ['女', '男']
      this.form.age = this.context.person.age;
      this.form.sex = sexs[this.context.person.sex] || '未知';
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
        age: undefined,
        sex: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '肌酐必须输入', trigger: 'blur' }],
        p2: [{ required: true, message: '尿素必须输入', trigger: 'blur' }],
        p3: [{ required: true, message: '尿酸必须输入', trigger: 'blur' }],
        p4: [{ required: true, message: '二氧化碳必须输入', trigger: 'blur' }],
        p5: [{ required: true, message: '磷必须输入', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleNumberInput(event, field) {
      let value = event.replace(/[^0-9.]/g, '');

      // 处理多个小数点的情况
      const decimalParts = value.split('.');
      if (decimalParts.length > 2) {
        value = decimalParts[0] + '.' + decimalParts.slice(1).join('');
      }

      // 限制整数部分为3位
      let max = ['p1', 'p3'].includes(field) ? 3 : 2;
      if (decimalParts[0].length > max) {
        value = decimalParts[0].substring(0, max) + (decimalParts[1] ? '.' + decimalParts[1] : '');
      }

      // 限制小数部分为2位
      if (decimalParts.length > 1 && decimalParts[1].length > 1) {
        value = decimalParts[0] + '.' + decimalParts[1].substring(0, 1);
      }
      this.form[field] = value;
    },
    handleBlur(field) {
      if (this.form[field] != undefined && this.form[field] != '') {
        this.form[field] = parseFloat(this.form[field]).toFixed(1);
      }
    },
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
