<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini" style="overflow-y: auto">
      <el-form-item prop="p1" label="1、您是否曾经患有以下疾病（多选）">
        <el-checkbox-group v-model="form.p1" @change="handleChange">
            <el-checkbox label="高血压" value="高血压" border></el-checkbox>
            <el-checkbox label="糖尿病" value="糖尿病" border></el-checkbox>
            <el-checkbox label="冠心病" value="冠心病" border></el-checkbox>
            <el-checkbox label="房颤" value="房颤" border></el-checkbox>
            <el-checkbox label="脑卒中" value="脑卒中" border></el-checkbox>
            <el-checkbox label="心衰" value="心衰" border></el-checkbox>
            <el-checkbox label="周围血管病" value="周围血管病" border></el-checkbox>
            <el-checkbox label="类风湿关节炎" value="类风湿关节炎" border></el-checkbox>
            <el-checkbox label="系统性红斑狼疮" value="系统性红斑狼疮" border></el-checkbox>
            <el-checkbox label="肝肾功能不全等病史" value="肝肾功能不全等病史" border></el-checkbox>
            <el-checkbox label="不存在以上情况" value="不存在以上情况" border></el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0087',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: []
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '是否曾经患有以下疾病必须选择', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleChange(e) {
      if (e.length > 1 && e[0] == '不存在以上情况') {
        this.form.p1 = e.splice(1)
      } else if (e.length > 1 && e.indexOf('不存在以上情况') != -1) {
        this.form.p1 = ['不存在以上情况']
      }
    },
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
<style scoped>
/deep/.el-checkbox--mini.is-bordered {
    padding: 6px 15px 0 15px !important;
    border-radius: 3px;
    height: 28px;
  }
  
  /deep/.el-checkbox__input {
    display: none !important;
    white-space: nowrap;
    cursor: pointer;
    outline: 0;
    line-height: 1;
    vertical-align: middle;
  }
  
  /deep/.el-checkbox__label {
    padding-left: 0px;
  }
  
  /deep/.el-form-item__label {
    text-align: right;
    vertical-align: middle;
    /* float: left; */
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    box-sizing: border-box;
    font-weight: 450;
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
    color: #000000;
  }
  
  /deep/.el-checkbox-group {
    font-size: 0;
    margin-top: 7px;
  }
  
  /deep/.el-checkbox {
    color: #000000;
    font-weight: 400;
    line-height: 1;
    cursor: pointer;
    white-space: nowrap;
    outline: 0;
    margin-right: 10px;
    margin-bottom: 10px;
  }
</style>