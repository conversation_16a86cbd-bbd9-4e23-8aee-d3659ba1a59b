<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="right"
            label-width="110px"
            size="mini"
        >
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="出生日期:" prop="p1">
                        <el-date-picker
                            v-model="form.p1"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="选择日期"
                            style="width: 150px"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="性别:" prop="p2">
                        <el-radio-group v-model="form.p2">
                            <el-radio label="男" />
                            <el-radio label="女" />
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="名族:" prop="p3">
                        <el-select v-model="form.p3" filterable placeholder="请选择">
                          <el-option label="汉族" value="汉族"></el-option>
                          <el-option label="蒙古族" value="蒙古族"></el-option>
                          <el-option label="回族" value="回族"></el-option>
                          <el-option label="藏族" value="藏族"></el-option>
                          <el-option label="维吾尔族" value="维吾尔族"></el-option>
                          <el-option label="苗族" value="苗族"></el-option>
                          <el-option label="彝族" value="彝族"></el-option>
                          <el-option label="壮族" value="壮族"></el-option>
                          <el-option label="布依族" value="布依族"></el-option>
                          <el-option label="朝鲜族" value="朝鲜族"></el-option>
                          <el-option label="满族" value="满族"></el-option>
                          <el-option label="侗族" value="侗族"></el-option>
                          <el-option label="瑶族" value="瑶族"></el-option>
                          <el-option label="白族" value="白族"></el-option>
                          <el-option label="土家族" value="土家族"></el-option>
                          <el-option label="哈尼族" value="哈尼族"></el-option>
                          <el-option label="哈萨克族" value="哈萨克族"></el-option>
                          <el-option label="傣族" value="傣族"></el-option>
                          <el-option label="黎族" value="黎族"></el-option>
                          <el-option label="傈僳族" value="傈僳族"></el-option>
                          <el-option label="佤族" value="佤族"></el-option>
                          <el-option label="畲族" value="畲族"></el-option>
                          <el-option label="高山族" value="高山族"></el-option>
                          <el-option label="拉祜族" value="拉祜族"></el-option>
                          <el-option label="水族" value="水族"></el-option>
                          <el-option label="东乡族" value="东乡族"></el-option>
                          <el-option label="纳西族" value="纳西族"></el-option>
                          <el-option label="景颇族" value="景颇族"></el-option>
                          <el-option label="柯尔克孜族" value="柯尔克孜族"></el-option>
                          <el-option label="达斡尔族" value="达斡尔族"></el-option>
                          <el-option label="仫佬族" value="仫佬族"></el-option>
                          <el-option label="羌族" value="羌族"></el-option>
                          <el-option label="布朗族" value="布朗族"></el-option>
                          <el-option label="撒拉族" value="撒拉族"></el-option>
                          <el-option label="毛南族" value="毛南族"></el-option>
                          <el-option label="仡佬族" value="仡佬族"></el-option>
                          <el-option label="锡伯族" value="锡伯族"></el-option>
                          <el-option label="阿昌族" value="阿昌族"></el-option>
                          <el-option label="普米族" value="普米族"></el-option>
                          <el-option label="塔吉克族" value="塔吉克族"></el-option>
                          <el-option label="怒族" value="怒族"></el-option>
                          <el-option label="乌兹别克族" value="乌兹别克族"></el-option>
                          <el-option label="俄罗斯族" value="俄罗斯族"></el-option>
                          <el-option label="鄂温克族" value="鄂温克族"></el-option>
                          <el-option label="德昂族" value="德昂族"></el-option>
                          <el-option label="保安族" value="保安族"></el-option>
                          <el-option label="裕固族" value="裕固族"></el-option>
                          <el-option label="京族" value="京族"></el-option>
                          <el-option label="塔塔尔族" value="塔塔尔族"></el-option>
                          <el-option label="独龙族" value="独龙族"></el-option>
                          <el-option label="鄂伦春族" value="鄂伦春族"></el-option>
                          <el-option label="赫哲族" value="赫哲族"></el-option>
                          <el-option label="门巴族" value="门巴族"></el-option>
                          <el-option label="珞巴族" value="珞巴族"></el-option>
                          <el-option label="基诺族" value="基诺族"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="婚否:" prop="p4">
                        <el-radio-group v-model="form.p4">
                            <el-radio label="是" />
                            <el-radio label="否" />
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="身份证号码:" prop="p5">
                        <el-input
                            v-model="form.p5"
                            style="width: 150px"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="手机号码:" prop="p6">
                        <el-input
                            v-model="form.p6"
                            style="width: 120px"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="身高:" prop="p7">
                        <el-input
                            v-model="form.p7"
                            style="width: 150px"
                            @change="handleChangeP7"
                            ><template slot="append">cm</template></el-input
                        >
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="体重:" prop="p8">
                        <el-input
                            v-model="form.p8"
                            style="width: 150px"
                            @change="handleChangeP8"
                            ><template slot="append">kg</template></el-input
                        >
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="药物过敏史" prop="p9">
                        <el-radio-group v-model="form.p9">
                            <el-radio label="无" />
                            <el-radio label="有" />
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="14" style="position: relative; left: -70px">
                    <el-form-item label="" prop="p10">
                        <el-input
                            v-model="form.p10"
                            style="width: 250px"
                            maxlength="50"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="职业:" prop="p11">
                <el-select
                    v-model="form.p11"
                    placeholder="请选择"
                    style="width: 200px"
                    multiple
                >
                    <el-option
                        label="1.国家机关、党群组织、企业、事业单位负责人"
                        value="1.国家机关、党群组织、企业、事业单位负责人"
                    >
                    </el-option>
                    <el-option label="2.专业技术人员" value="2.专业技术人员">
                    </el-option>
                    <el-option
                        label="3.办事人员和有关人员"
                        value="3.办事人员和有关人员"
                    >
                    </el-option>
                    <el-option
                        label="4.商业、服务人员"
                        value="4.商业、服务人员"
                    >
                    </el-option>
                    <el-option
                        label="5.农、林、牧、渔、水利业生产人员"
                        value="5.农、林、牧、渔、水利业生产人员"
                    >
                    </el-option>
                    <el-option
                        label="6.生产、运输设备操作人员及有关人员"
                        value="6.生产、运输设备操作人员及有关人员"
                    >
                    </el-option>
                    <el-option label="7.军人" value="7.军人"> </el-option>
                    <el-option label="8.其他" value="8.其他"> </el-option>
                </el-select>
                <el-input
                    v-model="form.p12"
                    style="width: 250px; margin-left: 20px"
                    maxlength="20"
                ></el-input>
            </el-form-item>

            <el-form-item label="医疗费用来源:" prop="p13">
                <el-select
                    v-model="form.p13"
                    placeholder="请选择"
                    style="width: 200px"
                    multiple
                >
                    <el-option label="1.公费" value="1.公费"> </el-option>
                    <el-option label="2.基本医疗保险" value="2.基本医疗保险">
                    </el-option>
                    <el-option label="3.商业保险" value="3.商业保险">
                    </el-option>
                    <el-option label="4.农村合作医疗" value="4.农村合作医疗">
                    </el-option>
                    <el-option label="5.自费" value="5.自费"> </el-option>
                    <el-option label="6.其他" value="6.其他"> </el-option>
                </el-select>
                <el-input
                    v-model="form.p14"
                    style="width: 250px; margin-left: 20px"
                ></el-input>
            </el-form-item>
            <el-form-item label="重要病史" prop="p15">
                <el-input
                    v-model="form.p15"
                    style="width: 80%"
                    type="textarea"
                    maxlength="500"
                    show-word-limit
                ></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0013",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        var checkP7 = (rule, value, callback) => {
            if ((value == "" && value != 0) || (value == null && value != 0)) {
                return callback(new Error("身高不能为空"));
            }
            if (Number.isNaN(Number(value))) {
                callback(new Error("请输入数字值"));
            } else {
                if (value < 20 || value > 300) {
                    callback(new Error("身高范围在20cm-300cm之间"));
                } else {
                    callback();
                }
            }
        };
        var checkP8 = (rule, value, callback) => {
            console.log('体重:', value)
            if ((value == "" && value != 0) || (value == null && value != 0)) {
                return callback(new Error("体重不能为空"));
            }
            console.log(value);

            if (Number.isNaN(Number(value))) {
                callback(new Error("请输入数字值"));
            } else {
                if (value < 0 || value > 650) {
                    callback(new Error("体重范围在0kg-650kg之间"));
                } else {
                    callback();
                }
            }
        };
        return {
            form: {
                p3: [],
            },
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: true,
                        message: "请选择出生日期",
                        trigger: "change",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "请选择性别",
                        trigger: "change",
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "请选择民族",
                        trigger: "blur",
                    },
                ],
                p4: [
                    {
                        required: true,
                        message: "请选择婚姻状态",
                        trigger: "change",
                    },
                ],
                p5: [
                    {
                        required: true,
                        message: "请输入身份证号码",
                        trigger: "blur",
                    },
                ],
                p6: [
                    {
                        required: true,
                        message: "请输入手机号码",
                        trigger: "blur",
                    },
                ],
                p7: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: checkP7,
                    },
                ],
                p8: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: checkP8,
                    },
                ],
                p9: [
                    {
                        required: true,
                        message: "请选择药物过敏史",
                        trigger: "change",
                    },
                ],
                p11: [
                    {
                        required: true,
                        message: "请选择职业",
                        trigger: "blur",
                    },
                ],
                p13: [
                    {
                        required: true,
                        message: "请选择医疗费用来源",
                        trigger: "blur",
                    },
                ],
                p15: [
                    {
                        required: true,
                        message: "请输入重要病史",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        handleChangeP7(value) {
            if (!Number.isNaN(Number(value))) {
                this.form.p7 = value = Number(
                    value.replace(/(\.\d{3})\d*/, "$1").slice(0, 6)
                ).toFixed(1);
            } else {
                console.log("我不是数字 ");
            }
        },
        handleChangeP8(value) {
            if (!Number.isNaN(Number(value))) {
                this.form.p8 = value = Number(
                    value.replace(/(\.\d{3})\d*/, "$1").slice(0, 7)
                ).toFixed(2);
            } else {
                console.log("我不是数字 ");
            }
        },
    },
};
</script>
