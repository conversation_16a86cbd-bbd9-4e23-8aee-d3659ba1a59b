<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="1、关节狭窄评分" prop="p1">
                        <el-row type="flex" style="width: 150px ;">
                            <el-input-number :controls="false" v-model="form.p1" style="flex: 1;"></el-input-number>
                            <div class="spanPos">分</div>
                        </el-row>
                        
                    </el-form-item>
                </el-col>
            </el-row>
    
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="2、骨侵蚀评分" prop="p2">
                        <el-row type="flex" style="width: 150px ;">
                            <el-input-number :controls="false" v-model="form.p2" style="width: 150px"
                                ></el-input-number>

                            <div class="spanPos">分</div>
                        </el-row>
                    </el-form-item>
                </el-col>
            </el-row>


            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="3、总评分" prop="p3">
                        <el-row type="flex" style="width: 150px ;">
                            <el-input-number :controls="false" v-model="form.p3" style="width: 150px"
                                ></el-input-number>

                            <div class="spanPos">分</div>
                        </el-row>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0044",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {

                p1: undefined,
                p2: undefined,
                p3: undefined,
            },
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: true,
                        message: "请输入关节狭窄评分",
                        trigger: "blur",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "请输入骨侵蚀评分",
                        trigger: "blur",
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "请输入总评分",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>

<style scoped>
/deep/.el-input__inner {
    text-align: center;
}


/deep/.el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.spanPos{
    height: 27px;
    background-color: #F5F7FA;
    color: #909399;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    padding: 0 20px;
    white-space: nowrap;

    font-size: 12px;
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/deep/.el-form-item__label{
    font-weight: 450;
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
    color: #000000;
}
</style>