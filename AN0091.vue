<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="mini" label-position="top" label-width="80px" style="overflow-y: auto">
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p1" label="血清肌酐">
            <el-input v-model="form.p1" @input="handleNumberInput($event, 'p1')">
              <template slot="append">μmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p2" label="甘油三酯">
            <el-input v-model="form.p2" @input="handleNumberInput($event, 'p2')" @blur="handleBlur('p2')">
              <template slot="append">mmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p3" label="血清尿酸">
            <el-input v-model="form.p3" @input="handleNumberInput($event, 'p3')">
              <template slot="append">μmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p4" label="总胆固醇">
            <el-input v-model="form.p4" @input="handleNumberInput($event, 'p4')" @blur="handleBlur('p4')">
              <template slot="append">mmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p5" label="低密度脂蛋白胆固醇">
            <el-input v-model="form.p5" @input="handleNumberInput($event, 'p5')" @blur="handleBlur('p5')">
              <template slot="append">mmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p6" label="高密度脂蛋白胆固醇">
            <el-input v-model="form.p6" @input="handleNumberInput($event, 'p6')" @blur="handleBlur('p6')">
              <template slot="append">mmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0091',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '血清肌酐必须输入', trigger: 'blur' }],
        p2: [{ required: true, message: '甘油三酯必须输入', trigger: 'blur' }],
        p3: [{ required: true, message: '血清尿酸必须输入', trigger: 'blur' }],
        p4: [{ required: true, message: '总胆固醇必须输入', trigger: 'blur' }],
        p5: [{ required: true, message: '低密度脂蛋白胆固醇必须输入', trigger: 'blur' }],
        p6: [{ required: true, message: '高密度脂蛋白胆固醇必须输入', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleNumberInput(event, field) {
      if (['p2', 'p4', 'p5', 'p6'].includes(field)) {
        let value = event.replace(/[^0-9.]/g, '');

        // 处理多个小数点的情况
        const decimalParts = value.split('.');
        if (decimalParts.length > 2) {
          value = decimalParts[0] + '.' + decimalParts.slice(1).join('');
        }

        // 限制整数部分为3位
        if (decimalParts[0].length > 2) {
          value = decimalParts[0].substring(0, 2) + (decimalParts[1] ? '.' + decimalParts[1] : '');
        }

        // 限制小数部分为2位
        if (decimalParts.length > 1 && decimalParts[1].length > 2) {
          value = decimalParts[0] + '.' + decimalParts[1].substring(0, 2);
        }
        this.form[field] = value;
      } else {
        // 移除非数字字符
        let num = event.replace(/\D/g, '')
        
        // 限制长度为3位
        if (num.length > 3) {
          num = num.slice(0, 3)
        }
        // 更新绑定值
        this.form[field] = num
      }

    },
    handleBlur(field) {
      if (this.form[field] != undefined && this.form[field] != '') {
        this.form[field] = parseFloat(this.form[field]).toFixed(2);
      }
    },
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
