<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            label-width="80px"
            style="overflow-y: auto"
            size="mini"
        >
            <el-form-item label="1、是否有合并用药/非药物治疗" prop="p1">
                <el-radio-group v-model="form.p1" @change="handleChangeP1">
                    <el-radio label="是" border>是</el-radio>
                    <el-radio label="否" border>否</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="2、合并用药/非药物治疗详细描述" prop="p2">
                <el-table
                    :data="form.p2"
                    border
                    style="width: 100%"
                    size="mini"
                >
                    <el-table-column
                        type="index"
                        label="序号"
                        width="50"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="p1"
                        label="药物/非药物治疗名称"
                        width="200"
                        header-align="center"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p1"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入治疗名称"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p2"
                        label="原因"
                        width="200"
                        header-align="center"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p2"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入原因"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p3"
                        label="单日剂量"
                        width="200"
                        header-align="center"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p3"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入单日剂量"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p4"
                        label="用法"
                        width="200"
                        header-align="center"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p4"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入用法"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p5"
                        label="开始日期"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                                v-model="scope.row.p5"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期"
                                style="width: 130px"
                            ></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p6"
                        label="结束日期"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                                v-model="scope.row.p6"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期"
                                style="width: 130px"
                            ></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p7"
                        label="对类风湿性关节炎治疗的影响"
                        width="150"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-radio-group v-model="scope.row.p7" size="mini">
                                <el-radio label="是" />
                                <el-radio label="否" />
                            </el-radio-group>
                        </template>
                    </el-table-column>
                    <el-table-column width="40" header-align="center">
                        <template slot="header" slot-scope="scope">
                            <i
                                class="el-icon-circle-plus"
                                style="
                                    color: #409eff;
                                    cursor: pointer;
                                    font-size: 18px;
                                "
                                @click="add('p2')"
                            ></i>
                        </template>

                        <template slot-scope="scope">
                            <i
                                class="el-icon-remove"
                                style="
                                    color: red;
                                    cursor: pointer;
                                    font-size: 18px;
                                "
                                @click="del(scope.$index, 'p2')"
                            ></i>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0045",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p2: [],
            },
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "是否有合并用药/非药物治疗",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        add(key) {
            switch (key) {
                case "p2":
                    this.form[key].push({
                        p1: "",
                        p2: "",
                        p3: "",
                        p4: "",
                        p5: "",
                        p6: "",
                        p7: "是",
                    });
                    break;
                default:
                    break;
            }
        },
        del(index, key) {
            this.form[key].splice(index, 1);
        },
        handleChangeP1(value) {
            if (value == "是") {
                this.add("p2");
            }
        },
    },
};
</script>
<style scoped>
.mb-10 {
    margin-bottom: 10px;
}
::v-deep .el-radio--mini.is-bordered .el-radio__inner {
    display: none !important;
}

::v-deep .el-radio {
    margin-right: 10px;
}
</style>
