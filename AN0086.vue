<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini" style="overflow-y: auto">
      <el-form-item prop="p1" label="1、您是否被临床诊断为已下疾病或状态（多选）">
        <el-checkbox-group v-model="form.p1" @change="handleChange">
            <el-checkbox label="急性冠状动脉综合征" value="急性冠状动脉综合征" border></el-checkbox>
            <el-checkbox label="稳定性冠状动脉粥样硬化性心脏病" value="稳定性冠状动脉粥样硬化性心脏病" border></el-checkbox>
            <el-checkbox label="血运重建术后" value="血运重建术后" border></el-checkbox>
            <el-checkbox label="缺血性心肌病" value="缺血性心肌病" border></el-checkbox>
            <el-checkbox label="外周动脉粥样硬化性疾病" value="外周动脉粥样硬化性疾病" border></el-checkbox>
            <el-checkbox label="不存在以上情况" value="不存在以上情况" border></el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item prop="p2" label="2、您是否为40岁及以上糖尿病患者（单选）">
        <el-radio-group v-model="form.p2">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0086',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: []
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '是否被临床诊断为已下疾病或状态必须选择', trigger: 'blur' }],
        p2: [{ required: true, message: '是否为40岁及以上糖尿病患者必须选择', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleChange(e) {
      if (e.length > 1 && e[0] == '不存在以上情况') {
        this.form.p1 = e.splice(1)
      } else if (e.length > 1 && e.indexOf('不存在以上情况') != -1) {
        this.form.p1 = ['不存在以上情况']
      }
    },
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
<style scoped>
/deep/.el-checkbox--mini.is-bordered {
    padding: 6px 15px 0 15px !important;
    border-radius: 3px;
    height: 28px;
  }
  
  /deep/.el-checkbox__input {
    display: none !important;
    white-space: nowrap;
    cursor: pointer;
    outline: 0;
    line-height: 1;
    vertical-align: middle;
  }
  
  /deep/.el-checkbox__label {
    padding-left: 0px;
  }
  
  /deep/.el-form-item__label {
    text-align: right;
    vertical-align: middle;
    /* float: left; */
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    box-sizing: border-box;
    font-weight: 450;
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
    color: #000000;
  }
  
  /deep/.el-checkbox-group {
    font-size: 0;
    margin-top: 7px;
  }
  
  /deep/.el-checkbox {
    color: #000000;
    font-weight: 400;
    line-height: 1;
    cursor: pointer;
    white-space: nowrap;
    outline: 0;
    margin-right: 10px;
    margin-bottom: 10px;
  }

  /deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item__label {
  /* text-align: right; */
  vertical-align: middle;
  /* float: left; */
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>