<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="80px"
            style="overflow-y: auto"
            label-position="top"
        >
            <el-form-item
                label="请在下图中标记患者肿胀的关节（点击相应关节圆圈）"
                required
            >
                <div class="body-bg">
                    <div
                        v-for="item in dotList"
                        :key="item.p1"
                        :style="item.style"
                        :class="item.class"
                        @click="clickDot(item)"
                    ></div>
                </div>
            </el-form-item>
            <el-form-item
                label="28个关节肿胀总数："
                prop="p1"
                style="display: flex"
            >
                <el-input
                    v-model="form.p1"
                    :disabled="true"
                    style="width: 200px"
                ></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0037",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
            this.dotList = this.form.p2;
        }
    },
    data() {
        return {
            form: {
                p1: 0,
            },
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "请选择患者肿胀的关节",
                        trigger: "blur",
                    },
                ],
            },
            dotList: [
                {
                    p1: 1,
                    class: "dot-common",
                    click: false,
                    style: "width:60px;height:60px;left:34%;top:3px;pointer-events: none",
                },
                {
                    p1: 2,
                    class: "dot-common",
                    click: false,
                    style: "width:20px;height:20px;left:21%;top:82px",
                },
                {
                    p1: 3,
                    class: "dot-common",
                    click: false,
                    style: "width:20px;height:20px;right:84px;top:82px",
                },
                {
                    p1: 4,
                    class: "dot-common",
                    click: false,
                    style: "width:20px;height:20px;left:37px;top:128px",
                },
                {
                    p1: 5,
                    class: "dot-common",
                    click: false,
                    style: "width:20px;height:20px;right:63px;top:128px",
                },
                {
                    p1: 6,
                    class: "dot-common",
                    click: false,
                    style: "width:17px;height:17px;left:23px;top:60%",
                },

                {
                    p1: 7,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;left:5px;top:68.5%",
                },
                {
                    p1: 8,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;left:5px;top:74.5%",
                },
                {
                    p1: 9,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;left:16px;top:72%",
                },
                {
                    p1: 10,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;left:16px;top:78%",
                },
                {
                    p1: 11,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;left:30px;top:72%",
                },
                {
                    p1: 12,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;left:30px;top:78%",
                },
                {
                    p1: 13,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;left:44px;top:72%",
                },
                {
                    p1: 14,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;left:44px;top:78%",
                },

                {
                    p1: 15, // 大拇指
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;left:52px;top:65%",
                },
                {
                    p1: 16, // 大拇指
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;left:65px;top:67%",
                },
                {
                    p1: 17, //右手
                    class: "dot-common",
                    click: false,
                    style: "width:17px;height:17px;right:49px;top:60%",
                },

                {
                    p1: 18,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;right:31px;top:68.5%",
                },
                {
                    p1: 19,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;right:31px;top:74.5%",
                },
                {
                    p1: 20,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;right:41px;top:72%",
                },
                {
                    p1: 21,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;right:41px;top:78%",
                },
                {
                    p1: 22,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;right:55px;top:72%",
                },
                {
                    p1: 23,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;right:55px;top:78%",
                },
                {
                    p1: 24,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;right:69px;top:72%",
                },
                {
                    p1: 25,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;right:69px;top:78%",
                },
                {
                    p1: 26,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;right:77px;top:65%",
                },
                {
                    p1: 27,
                    class: "dot-common",
                    click: false,
                    style: "width:6px;height:6px;right:90px;top:67%",
                },
                {
                    p1: 28,
                    class: "dot-common",
                    click: false,
                    style: "width:25px;height:25px;left:69px;bottom:32px",
                },
                {
                    p1: 29,
                    class: "dot-common",
                    click: false,
                    style: "width:25px;height:25px;right:95px;bottom:32px",
                },
            ],
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            this.form.p2 = this.dotList;

            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        clickDot(item) {
            item.click = !item.click;
            if (item.click) {
                item.class = "dot-common dot-click";
            } else {
                item.class = "dot-common";
            }
            this.form.p1 = this.dotList.filter((item) => item.click).length;
        },
    },
};
</script>
<style scoped>
.body-bg {
    position: relative;
    width: 276px;
    height: 280px;
    background-image: url('./assets/images/body.png');
    background-size: cover;
}

.dot-common {
    border: 2px solid #000;
    border-radius: 50%;
    position: absolute;
    cursor: pointer;
    background-color: #fff;
}
.dot-click {
    background-color: rgb(236, 128, 141);
}
</style>
