<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="mini" inline>
      <el-form-item v-for="item in questions" :key="item.prop" :prop="item.prop" :label="item.label">
        <el-radio-group v-model="form[item.prop]">
          <el-radio v-for="option in item.options" :key="option" :label="option" border />
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "AN0050",
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != "") {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      questions: [
        { label: "1、S（力量）：您提起4.5 kg的东西有困难吗？（单选）", prop: "p1", options: ['没有困难', '有点困难','很困难/做不到']},
        { label: "2、A（辅助行走）：您行走穿过一间房间有困难吗？（单选）", prop: "p2", options: ['没有困难', '有点困难','很困难，需要帮助/做不到']},
        { label: "3、R（从椅子上起身）：您从椅子上或床上起身有困难吗？（单选）", prop: "p3", options: ['没有困难', '有点困难','很困难，需要辅助设备才能做到']},
        { label: "4、C（爬楼梯）：您爬十级台阶有困难吗？（单选）", prop: "p4", options: ['没有困难', '有点困难','很困难/做不到']},
        { label: "5、F（跌倒）：您近一年内跌倒过几次？（单选）", prop: "p5", options: ['没有', '1~3次','4次及以上']},
      ],
      form: {
        p1: undefined,
        p2: undefined,
        p3: undefined,
        p4: undefined,
        p5: undefined,
      },
      // 表单校验
      rules: {
        p1: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p2: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p3: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p4: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p5: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ]

      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      console.log(this.form)
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
<style scoped>
/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item__label {
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-form-item__content {
  width: 100%;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>
