<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            label-width="80px"
            style="overflow-y: auto"
            size="mini"
        >
            <el-form-item label="是否存在不良事件" prop="p1">
                <el-radio-group v-model="form.p1">
                    <el-radio label="是" border>是</el-radio>
                    <el-radio label="否" border>否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-tabs v-show="form.p1 == '是'" v-model="currentName" type="border-card" class="tabs-card" editable @edit="handleEdit">
                  <el-tab-pane :label="item.p1" :name="index + ''" v-for="(item, index) in form.p2" :key="index">
                      <div>不良事件详细描述</div>
                      <el-form-item label="1、不良事件名称">
                          <el-input
                              v-model="item.p1"
                              style="width: 250px"
                              maxlength="100"
                          ></el-input>
                      </el-form-item>
                      <el-form-item label="2、开始发生日期" prop="p3">
                          <el-date-picker
                              v-model="item.p2"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="选择日期"
                              style="width: 150px"
                          >
                          </el-date-picker>
                      </el-form-item>
                      <el-form-item label="3、终止日期" prop="p4">
                          <el-date-picker
                              v-model="item.p3"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="选择日期"
                              style="width: 150px"
                          >
                          </el-date-picker>
                      </el-form-item>
                      <el-form-item label="4、严重程度" prop="p5">
                          <el-radio-group v-model="item.p4">
                              <el-radio label="轻" border>轻</el-radio>
                              <el-radio label="中" border>中</el-radio>
                              <el-radio label="重" border>重</el-radio>
                          </el-radio-group>
                      </el-form-item>
                      <el-form-item label="5、采取措施" prop="p6">
                          <el-radio-group v-model="item.p5">
                              <el-radio label="无任何措施" border>无任何措施</el-radio>
                              <el-radio label="暂停用药/剂量调整" border>暂停用药/剂量调整</el-radio>
                              <el-radio label="中止试验" border>中止试验</el-radio>
                              <el-radio label="采取合并用药" border>采取合并用药</el-radio>
                              <el-radio label="住院或者住院延长" border>住院或者住院延长</el-radio>
                          </el-radio-group>
                      </el-form-item>
                      <el-form-item label="6、与研究药物的关系" prop="p7">
                          <el-radio-group v-model="item.p6">
                              <el-radio label="可能" border>可能</el-radio>
                              <el-radio label="很可能" border>很可能</el-radio>
                              <el-radio label="可能相关" border>可能相关</el-radio>
                              <el-radio label="肯定无关" border>肯定无关</el-radio>
                          </el-radio-group>
                      </el-form-item>
                      <el-form-item label="7、是否存在不良事件" prop="p8">
                          <el-radio-group v-model="item.p7">
                              <el-radio label="是" border>是</el-radio>
                              <el-radio label="否" border>否</el-radio>
                          </el-radio-group>
                      </el-form-item>
                      <el-form-item label="8、是否存在不良事件" prop="p9">
                          <el-radio-group v-model="item.p8">
                              <el-radio label="痊愈" border>痊愈</el-radio>
                              <el-radio label="缓解" border>缓解</el-radio>
                              <el-radio label="无改变" border>无改变</el-radio>
                              <el-radio label="加重" border>加重</el-radio>
                              <el-radio label="死亡" border>死亡</el-radio>
                              <el-radio label="失访" border>失访</el-radio>
                          </el-radio-group>
                      </el-form-item>
                      <el-form-item label="9、是否存在不良事件" prop="p10">
                          <el-input
                              v-model="item.p9"
                              style="width: 400px"
                              type="textarea"
                              maxlength="1000"
                              show-word-limit
                              :rows="4"
                          ></el-input>
                      </el-form-item>
                  </el-tab-pane>
            </el-tabs>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0046",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            currentName: '',
            form: {
                p2: [
                    {
                        p1: '未命名不良事件',
                    }
                ]
            },
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "是否存在不良事件必须选择",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        handleEdit(targetName, action) {
            if (action === 'add') {
                if (this.form.p2.length >= 5) {
                    this.$message.error('最多添加5个不良事件！');
                    return;
                }
                let index = this.form.p2.length + 1
                this.form.p2.push({
                    // p1: '未命名不良事件',
                    p1: index
                });
            }
            if (action === 'remove') {
                this.form.p2.splice(targetName, 1);
                if (this.form.p2.length) {
                    if (this.currentName == targetName && this.form.p2.length - 1 < targetName) {
                        this.currentName = (this.form.p2.length - 1) + '';
                    } else if (targetName < this.currentName) {
                        this.currentName = (this.currentName - 1) + '';
                    }
                }
            }
        },
        /** 表单值 */
        getData() {
            if (this.form.p1 == '否') {
                this.form.p2 = []
            }
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            if (this.form.p1 == '是' && this.form.p2.length == 0) {
                this.$message.error('请添加不良事件！');
                return true
            }
            return rs;
        },
    },
};
</script>
<style scoped>
/deep/.el-tabs__new-tab {
    margin-right: 10px;
}
/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item__label {
  /* text-align: left; */
  vertical-align: middle;
  /* float: left; */
  color: #606266;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-form-item__content {
  width: 100%;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}
/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin: 0px 15px 10px 0px !important;
}
</style>
