<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="mini" label-position="top" label-width="80px" style="overflow-y: auto">
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p1" label="总蛋白">
            <el-input v-model="form.p1" @input="handleNumberInput($event, 'p1')" @blur="handleBlur('p1')">
              <template slot="append">g/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p2" label="白蛋白">
            <el-input v-model="form.p2" @input="handleNumberInput($event, 'p2')" @blur="handleBlur('p2')">
              <template slot="append">g/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p3" label="球蛋白">
            <el-input v-model="form.p3" @input="handleNumberInput($event, 'p3')" @blur="handleBlur('p3')">
              <template slot="append">g/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p4" label="白球比例">
            <el-input v-model="form.p4" :disabled="true">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p5" label="总胆红素">
            <el-input v-model="form.p5" @input="handleNumberInput($event, 'p5')" @blur="handleBlur('p5')">
              <template slot="append">µmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p6" label="直接胆红素">
            <el-input v-model="form.p6" @input="handleNumberInput($event, 'p6')" @blur="handleBlur('p6')">
              <template slot="append">µmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p7" label="间接胆红素">
            <el-input v-model="form.p7" @input="handleNumberInput($event, 'p7')" @blur="handleBlur('p7')">
              <template slot="append">µmol/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p8" label="γ-谷氨酰基转移酶">
            <el-input v-model="form.p8" @input="handleNumberInput($event, 'p8')" @blur="handleBlur('p8')">
              <template slot="append">U/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p9" label="丙氨酸氨基转移酶">
            <el-input v-model="form.p9" @input="handleNumberInput($event, 'p9')" @blur="handleBlur('p9')">
              <template slot="append">U/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p10" label="碱性磷酸酶">
            <el-input v-model="form.p10" @input="handleNumberInput($event, 'p10')" @blur="handleBlur('p10')">
              <template slot="append">U/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p11" label="天门冬氨酸氨基转移酶">
            <el-input v-model="form.p11" @input="handleNumberInput($event, 'p11')" @blur="handleBlur('p11')">
              <template slot="append">U/L</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0092',
  props: {
    formData: String,
    context: {
      type: Object,
      default: () => ({
        person: {}
      })
    }
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
    if (this.context?.person != null && this.context?.person != '') {
      let sexs = ['女', '男']
      this.form.age = this.context.person.age;
      this.form.sex = sexs[this.context.person.sex] || '未知';
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
        age: undefined,
        sex: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '总蛋白必须输入', trigger: 'blur' }],
        p2: [{ required: true, message: '白蛋白必须输入', trigger: 'blur' }],
        p3: [{ required: true, message: '球蛋白必须输入', trigger: 'blur' }],
        p4: [{ required: true, message: '白球比例必须输入', trigger: 'blur' }],
        p5: [{ required: true, message: '总胆红素必须输入', trigger: 'blur' }],
        p6: [{ required: true, message: '直接胆红素必须输入', trigger: 'blur' }],
        p7: [{ required: true, message: '间接胆红素必须输入', trigger: 'blur' }],
        p8: [{ required: true, message: 'γ-谷氨酰基转移酶必须输入', trigger: 'blur' }],
        p9: [{ required: true, message: '丙氨酸氨基转移酶必须输入', trigger: 'blur' }],
        p10: [{ required: true, message: '碱性磷酸酶必须输入', trigger: 'blur' }],
        p11: [{ required: true, message: '天门冬氨酸氨基转移酶必须输入', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleNumberInput(event, field) {
      let value = event.replace(/[^0-9.]/g, '');

      // 处理多个小数点的情况
      const decimalParts = value.split('.');
      if (decimalParts.length > 2) {
        value = decimalParts[0] + '.' + decimalParts.slice(1).join('');
      }

      // 限制整数部分为3位
      let max = ['p1', 'p2', 'p3', 'p8', 'p9', 'p10', 'p11'].includes(field) ? 3 : 2;
      if (decimalParts[0].length > max) {
        value = decimalParts[0].substring(0, max) + (decimalParts[1] ? '.' + decimalParts[1] : '');
      }

      // 限制小数部分为2位
      if (decimalParts.length > 1 && decimalParts[1].length > 1) {
        value = decimalParts[0] + '.' + decimalParts[1].substring(0, 1);
      }
      if (['p1', 'p2'].includes(field) && this.form.p1 && this.form.p2) {
        this.form.p4 = parseFloat(this.form.p2 / this.form.p1).toFixed(1);
      }
      this.form[field] = value;
    },
    handleBlur(field) {
      if (this.form[field] != undefined && this.form[field] != '') {
        this.form[field] = parseFloat(this.form[field]).toFixed(1);
      }
    },
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
