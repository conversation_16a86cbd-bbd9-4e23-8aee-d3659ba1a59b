<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="right"
            label-width="139px"
            size="mini"
        >
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="抗核抗体（滴度）:" prop="p1">
                        <el-input-number
                            v-model="form.p1"
                            style="width: 150px"
                            :precision="0"
                            :max="99999"
                            :min="0"
                        ></el-input-number>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="10" style="margin-top: 15px">
                <el-col :span="8">
                    <el-form-item label="抗核抗体核型:" prop="p2">
                        <el-input
                            v-model="form.p2"
                            style="width: 150px"
                            maxlength="10"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0028",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p1: undefined,
                p2: undefined,
            },
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: true,
                        message: "请输入抗核抗体（滴度）",
                        trigger: "blur",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "请输入抗核抗体核型",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
<style scoped>
.unit {
    border: 1px solid #dcdfe6;
    text-wrap: nowrap;
}

/deep/.el-form-item__label {
    font-weight: 450;
    font-family: "PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC",
        sans-serif;
    color: #000000;
}
</style>
