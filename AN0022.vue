<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            label-width="80px"
            style="overflow-y: auto"
            size="mini"
        >
            <el-form-item label="1、首次诊断日期" prop="p1">
                <el-date-picker
                    v-model="form.p1"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                    style="width: 150px"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item label="2、药物使用详细描述" prop="p2">
                <el-table
                    :data="form.p2"
                    border
                    style="width: 100%"
                    size="mini"
                >
                    <el-table-column
                        type="index"
                        label="序号"
                        width="50"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="p1"
                        label="具体药品名称"
                        width="200"
                        header-align="center"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p1"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入具体药品名称"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p2"
                        label="单日剂量"
                        width="200"
                        header-align="center"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p2"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入单日剂量"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p3"
                        label="单周剂量"
                        width="200"
                        header-align="center"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p3"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入单周剂量"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p4"
                        label="开始日期"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                                v-model="scope.row.p4"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期"
                                style="width: 130px"
                            ></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p5"
                        label="结束日期"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                                v-model="scope.row.p5"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期"
                                style="width: 130px"
                            ></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column width="40" header-align="center">
                        <template slot="header" slot-scope="scope">
                            <i
                                class="el-icon-circle-plus"
                                style="
                                    color: #409eff;
                                    cursor: pointer;
                                    font-size: 18px;
                                "
                                @click="add('p2')"
                            ></i>
                        </template>

                        <template slot-scope="scope">
                            <i
                                class="el-icon-remove"
                                style="
                                    color: red;
                                    cursor: pointer;
                                    font-size: 18px;
                                "
                                @click="del(scope.$index, 'p2')"
                            ></i>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0022",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p2: [],
            },
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "本次的治疗方案:",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        add(key) {
            switch (key) {
                case "p2":
                    this.form[key].push({
                        p1: "",
                        p2: "",
                        p3: "",
                        p4: "",
                        p5: "",
                    });
                    break;
                default:
                    break;
            }
        },
        del(index, key) {
            this.form[key].splice(index, 1);
        },
    },
};
</script>
<style scoped>
.mb-10 {
    margin-bottom: 10px;
}
::v-deep .el-radio--mini.is-bordered .el-radio__inner {
    display: none !important;
}

::v-deep .el-radio {
    margin-right: 10px;
}
</style>
