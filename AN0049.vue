<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="mini" inline>
      <el-form-item v-for="item in questions" :key="item.prop" :prop="item.prop" :label="item.label">
        <el-radio-group v-model="form[item.prop]">
          <el-radio v-for="option in item.options" :key="option" :label="option" border />
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "AN0049",
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != "") {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      questions: [
        { label: "1、F（疲倦）：您会觉得疲倦吗？", prop: "p1", options: ['是', '否']},
        { label: "2、R（对抗力）：您不能够坚持走完一层楼的楼梯？", prop: "p2", options: ['是', '否']},
        { label: "3、A（自由活动）:您在不用任何辅助工具以及不用他人帮助的情况下，走完100米，是否有困难？", prop: "p3", options: ['是', '否']},
        { label: "4、I（疾病）：您目前有5种以上的疾病？", prop: "p4", options: ['是', '否']},
        { label: "5、L（体重下降）：您近6个月体重下降了有5%以上？", prop: "p5", options: ['是', '否']},
      ],
      form: {
        p1: undefined,
        p2: undefined,
        p3: undefined,
        p4: undefined,
        p5: undefined,
      },
      // 表单校验
      rules: {
        p1: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p2: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p3: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p4: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p5: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ]

      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      console.log(this.form)
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
<style scoped>
/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item {
  width: 100%;
}

/deep/.el-form-item__label {
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-form-item__content {
  width: 100%;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>
