<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <!-- 1、检查 -->
      <el-form-item prop="p1" label="1、检查">
        <!-- 穿刺方式 -->
        <el-form-item prop="p1PunctureMethod" label="（1）穿刺方式（可多选）">
          <el-checkbox-group v-model="form.p1PunctureMethod">
            <el-checkbox label="经直肠超声引导穿刺" border />
            <el-checkbox label="经会阴超声引导穿刺" border />
            <el-checkbox label="MRI - 超声融合靶向穿刺" border />
            <el-checkbox label="其他" border />
          </el-checkbox-group>
          <el-input
            v-if="form.p1PunctureMethod.includes('其他')"
            v-model="form.p1PunctureMethodOther"
            placeholder="请填写其他穿刺方式"
            maxlength="50"
            show-word-limit
            class="p1-puncture-other"
          />
        </el-form-item>

        <!-- 穿刺针数 -->
        <el-form-item prop="p1NeedleCount" label="（2）穿刺针数">
          <el-input
            v-model="form.p1NeedleCount"
            placeholder="请输入穿刺针数"
            style="width: 200px"
            @input="handleP1NeedleCountInput"
            @keypress="handleIntegerKeypress"
          />
          <span style="margin-left: 5px;">针</span>
        </el-form-item>
      </el-form-item>

      <!-- 2、确诊前列腺癌 -->
      <el-form-item prop="p2" label="2、确诊前列腺癌">
        <el-radio-group v-model="form.p2">
          <el-radio label="是" border/>
          <el-radio label="否" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 否：病理结果 -->
      <el-form-item v-if="form.p2 === '否'" prop="p2PathologyResult" label="病理结果为">
        <el-checkbox-group v-model="form.p2PathologyResult">
          <el-checkbox label="良性前列腺增生" border />
          <el-checkbox label="前列腺炎" border />
          <el-checkbox label="前列腺上皮内瘤变 PIN" border />
        </el-checkbox-group>
      </el-form-item>

      <!-- 是：癌细胞病理特征 -->
      <el-form-item v-if="form.p2 === '是'" prop="p2CancerFeatures" label="（1）癌细胞病理特征">
        <!-- 细胞异型性 -->
        <el-form-item prop="p2CellAtypia" label="细胞异型性">
          <el-radio-group v-model="form.p2CellAtypia" class="p2-atypia-group">
            <el-radio label="是" border />
            <el-radio label="否" border />
          </el-radio-group>
        </el-form-item>

        <!-- 免疫组化结果 -->
        <el-form-item prop="p2Immunohistochemistry" label="免疫组化结果">
          <div class="immunohisto-container">
            <div class="immunohisto-item">
              <span>PSA：</span>
              <el-radio-group v-model="form.p2PSA" class="immunohisto-radio">
                <el-radio label="+" border />
                <el-radio label="-" border />
              </el-radio-group>
            </div>
            <div class="immunohisto-item">
              <span>P504s：</span>
              <el-radio-group v-model="form.p2P504s" class="immunohisto-radio">
                <el-radio label="+" border />
                <el-radio label="-" border />
              </el-radio-group>
            </div>
            <div class="immunohisto-item">
              <span>CK34βE12：</span>
              <el-radio-group v-model="form.p2CK34βE12" class="immunohisto-radio">
                <el-radio label="+" border />
                <el-radio label="-" border />
              </el-radio-group>
            </div>
            <div class="immunohisto-item">
              <span>其他：</span>
              <el-input
                v-model="form.p2ImmunoOther"
                placeholder="请填写其他免疫组化结果"
                maxlength="100"
                show-word-limit
                style="width: 300px"
              />
            </div>
          </div>
        </el-form-item>
      </el-form-item>

      <!-- 癌组织占比 -->
      <el-form-item v-if="form.p2 === '是'" prop="p2CancerRatio" label="（2）癌组织占比">
        <el-input
          v-model="form.p2CancerRatio"
          placeholder="请输入癌组织占比（如：20%、50%、80%等）"
          style="width: 300px"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>

      <!-- 神经侵犯 -->
      <el-form-item v-if="form.p2 === '是'" prop="p2NerveInvasion" label="（3）神经侵犯">
        <el-radio-group v-model="form.p2NerveInvasion" class="p2-nerve-group">
          <el-radio label="有" border />
          <el-radio label="无" border />
        </el-radio-group>
      </el-form-item>

      <!-- 脉管侵犯 -->
      <el-form-item v-if="form.p2 === '是'" prop="p2VascularInvasion" label="（4）脉管侵犯">
        <el-radio-group v-model="form.p2VascularInvasion" class="p2-vascular-group">
          <el-radio label="有" border />
          <el-radio label="无" border />
        </el-radio-group>
      </el-form-item>

      <!-- 3、病理分级（Gleason评分） -->
      <el-form-item v-if="form.p2 === '是'" prop="p3" label="3、病理分级（Gleason评分）">
        <el-radio-group v-model="form.p3" class="p3-gleason-group">
          <el-radio label="ISUP 1 级" border />
          <el-radio label="ISUP 2 级" border />
          <el-radio label="ISUP 3 级" border />
          <el-radio label="ISUP 4 级" border />
          <el-radio label="ISUP 5 级" border />
        </el-radio-group>

        <!-- 分级详细说明 -->
        <div v-if="form.p3" class="gleason-description">
          <div v-if="form.p3 === 'ISUP 1 级'" class="grade-desc">
            Gleason评分≤6，仅由单个分离的、形态完好的腺体组成。
          </div>
          <div v-if="form.p3 === 'ISUP 2 级'" class="grade-desc">
            Gleason评分 3+4=7，主要由形态完好的腺体组成，伴有较少的形态发育不良腺体/融合腺体/筛状腺体组成。
          </div>
          <div v-if="form.p3 === 'ISUP 3 级'" class="grade-desc">
            Gleason评分 4+3=7，主要由发育不良的腺体/融合腺体/筛状腺体组成，伴少量形态完好的腺体。
          </div>
          <div v-if="form.p3 === 'ISUP 4 级'" class="grade-desc">
            Gleason评分 4+4=8、3+5=8、5+3=8，仅由发育不良的腺体/融合腺体/筛状腺体组成；或者以形态完好的腺体为主伴少量缺乏腺体分化的成分组成；或者以缺少腺体分化的成分为主伴少量形态完好的腺体组成。
          </div>
          <div v-if="form.p3 === 'ISUP 5 级'" class="grade-desc">
            Gleason评分 9～10，缺乏腺体形成结构（或伴坏死），伴或不伴腺体形态发育不良/融合腺体/筛状腺体。
          </div>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0161',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
      // 初始化所有可能的字段
      const fields = [
        'p1PunctureMethod', 'p1PunctureMethodOther', 'p1NeedleCount',
        'p2PathologyResult', 'p2CellAtypia', 'p2PSA', 'p2P504s', 'p2CK34βE12', 'p2ImmunoOther',
        'p2CancerRatio', 'p2NerveInvasion', 'p2VascularInvasion'
      ];

      fields.forEach(field => {
        if (this.form[field] === undefined) {
          if (field.includes('Method') || field.includes('Result')) {
            this.$set(this.form, field, []);
          } else {
            this.$set(this.form, field, '');
          }
        }
      });
    }
  },
  data() {
    // 验证函数
    const validateP1PunctureMethod = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error('请选择穿刺方式'));
      } else {
        callback();
      }
    };

    const validateP1PunctureMethodOther = (rule, value, callback) => {
      if (this.form.p1PunctureMethod.includes('其他') && !value) {
        callback(new Error('请填写其他穿刺方式'));
      } else {
        callback();
      }
    };

    const validateP1NeedleCount = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入穿刺针数'));
      } else if (!/^\d+$/.test(value)) {
        callback(new Error('请输入有效的整数'));
      } else if (parseInt(value) <= 0) {
        callback(new Error('穿刺针数必须大于0'));
      } else {
        callback();
      }
    };

    const validateP2PathologyResult = (rule, value, callback) => {
      if (this.form.p2 === '否' && (!value || value.length === 0)) {
        callback(new Error('请选择病理结果'));
      } else {
        callback();
      }
    };

    const validateP2CellAtypia = (rule, value, callback) => {
      if (this.form.p2 === '是' && !value) {
        callback(new Error('请选择细胞异型性'));
      } else {
        callback();
      }
    };

    const validateP2CancerRatio = (rule, value, callback) => {
      if (this.form.p2 === '是' && !value) {
        callback(new Error('请输入癌组织占比'));
      } else {
        callback();
      }
    };

    const validateP2NerveInvasion = (rule, value, callback) => {
      if (this.form.p2 === '是' && !value) {
        callback(new Error('请选择神经侵犯情况'));
      } else {
        callback();
      }
    };

    const validateP2VascularInvasion = (rule, value, callback) => {
      if (this.form.p2 === '是' && !value) {
        callback(new Error('请选择脉管侵犯情况'));
      } else {
        callback();
      }
    };

    const validateP3 = (rule, value, callback) => {
      if (this.form.p2 === '是' && !value) {
        callback(new Error('请选择病理分级'));
      } else {
        callback();
      }
    };

    return {
      form: {
        p1PunctureMethod: [],
        p1PunctureMethodOther: '',
        p1NeedleCount: '',
        p2: undefined,
        p2PathologyResult: [],
        p2CellAtypia: '',
        p2PSA: '',
        p2P504s: '',
        p2CK34βE12: '',
        p2ImmunoOther: '',
        p2CancerRatio: '',
        p2NerveInvasion: '',
        p2VascularInvasion: '',
        p3: '',
      },
      // 表单校验
      rules: {
        p1PunctureMethod: [{ validator: validateP1PunctureMethod, trigger: 'change' }],
        p1PunctureMethodOther: [{ validator: validateP1PunctureMethodOther, trigger: 'blur' }],
        p1NeedleCount: [{ validator: validateP1NeedleCount, trigger: 'blur' }],
        p2: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p2PathologyResult: [{ validator: validateP2PathologyResult, trigger: 'change' }],
        p2CellAtypia: [{ validator: validateP2CellAtypia, trigger: 'change' }],
        p2CancerRatio: [{ validator: validateP2CancerRatio, trigger: 'blur' }],
        p2NerveInvasion: [{ validator: validateP2NerveInvasion, trigger: 'change' }],
        p2VascularInvasion: [{ validator: validateP2VascularInvasion, trigger: 'change' }],
        p3: [{ validator: validateP3, trigger: 'change' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
    /** 处理按键事件，只允许整数 */
    handleIntegerKeypress(event) {
      const charCode = event.which ? event.which : event.keyCode;
      // 只允许数字(48-57)、退格(8)、删除(46)、方向键等控制键
      if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        event.preventDefault();
      }
    },
    /** 处理穿刺针数输入，只允许整数 */
    handleP1NeedleCountInput(value) {
      // 只保留数字
      const numericValue = value.replace(/[^\d]/g, '');
      this.form.p1NeedleCount = numericValue;
    },
  },
  watch: {
    'form.p1PunctureMethod'(val) {
      if (!val.includes('其他')) {
        this.form.p1PunctureMethodOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p1PunctureMethodOther');
        }
      }
    },
    'form.p2'(val) {
      if (val === '否') {
        // 清空"是"相关的字段
        this.form.p2CellAtypia = '';
        this.form.p2PSA = '';
        this.form.p2P504s = '';
        this.form.p2CK34βE12 = '';
        this.form.p2ImmunoOther = '';
        this.form.p2CancerRatio = '';
        this.form.p2NerveInvasion = '';
        this.form.p2VascularInvasion = '';
        this.form.p3 = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p2CellAtypia', 'p2CancerRatio', 'p2NerveInvasion', 'p2VascularInvasion', 'p3']);
        }
      } else if (val === '是') {
        // 清空"否"相关的字段
        this.form.p2PathologyResult = [];
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p2PathologyResult');
        }
      }
    },
  },
};
export const formConfig = {
  fieldList: [ {
      key:"p1PunctureMethod",
      label:"1、检查 - 穿刺方式"
    }, {
      key:"p1NeedleCount",
      label:"1、检查 - 穿刺针数"
    }, {
      key:"p2",
      label:"2、确诊前列腺癌（是 / 否）"
    }, {
      key:"p3",
      label:"3、病理分级（Gleason评分）"
    } ],
};
</script>
<style scoped>
::v-deep .el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

::v-deep .el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-radio__label {
  padding-left: 0px;
}

::v-deep .el-checkbox--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
  margin-right: 10px;
  margin-bottom: 10px;
}

::v-deep .el-checkbox__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-checkbox__label {
  padding-left: 0px;
}

::v-deep .el-form-item__label {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

::v-deep .el-radio-group {
  font-size: 0;
}

::v-deep .el-checkbox-group {
  font-size: 0;
}

::v-deep .el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

::v-deep .el-checkbox {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

.p1-puncture-other {
  width: 300px;
  margin-left: 10px;
  margin-top: 10px;
}

.p2-atypia-group,
.p2-nerve-group,
.p2-vascular-group,
.p3-gleason-group {
  display: inline-flex;
  align-items: center;
}

.immunohisto-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.immunohisto-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.immunohisto-item span {
  min-width: 80px;
  font-weight: 500;
}

.immunohisto-radio {
  display: inline-flex;
  gap: 5px;
}

.gleason-description {
  margin-top: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.grade-desc {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  font-weight: 400;
}

.p3-gleason-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.p3-gleason-group .el-radio {
  margin-bottom: 8px;
}
</style>
