<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            size="mini"
            inline
        >
            <el-form-item prop="p1" label="乙肝表面抗原（HbsAg）:">
                <el-radio-group v-model="form.p1">
                    <el-radio  label="阴性" border/>
                    <el-radio label="阳性" border/>
                    <el-radio label="未检测" border/>
                </el-radio-group>
            </el-form-item>

            <el-form-item prop="p2" label="乙肝表面抗体（HbsAb）:">
                <el-radio-group v-model="form.p2">
                    <el-radio  label="阴性" border/>
                    <el-radio label="阳性" border/>
                    <el-radio label="未检测" border/>
                </el-radio-group>
            </el-form-item>

            <el-form-item prop="p3" label="乙肝病毒e抗原（HbeAg）:">
                <el-radio-group v-model="form.p3">
                    <el-radio  label="阴性" border/>
                    <el-radio label="阳性" border/>
                    <el-radio label="未检测" border/>
                </el-radio-group>
            </el-form-item>

            <el-form-item prop="p4" label="乙肝病毒e抗体（HbeAb）:">
                <el-radio-group v-model="form.p4">
                    <el-radio  label="阴性" border/>
                    <el-radio label="阳性" border/>
                    <el-radio label="未检测" border/>
                </el-radio-group>
            </el-form-item>

            <el-form-item prop="p5" label="乙肝病毒核心抗体（HbcAb）:">
                <el-radio-group v-model="form.p5">
                    <el-radio  label="阴性" border/>
                    <el-radio label="阳性" border/>
                    <el-radio label="未检测" border/>
                </el-radio-group>
            </el-form-item>

        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0017",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p1: undefined,
                p2: undefined,
                p3: undefined,
                p4: undefined,
                p5: undefined,
            },
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "请选择乙肝表面抗原（HbsAg）",
                        trigger: "change",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "请选择乙肝表面抗体（HbsAb）",
                        trigger: "change",
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "请选择乙肝病毒e抗原（HbeAg）",
                        trigger: "change",
                    },
                ],
                p4: [
                    {
                        required: true,
                        message: "请选择乙肝病毒e抗体（HbeAb）",
                        trigger: "change",
                    },
                ],
                p5: [
                    {
                        required: true,
                        message: "请选择乙肝病毒核心抗体（HbcAb）",
                        trigger: "change",
                    },
                ]
                
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            console.log(this.form)
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
<style scoped>
/deep/.el-radio--mini.is-bordered {
    padding: 6px 15px 0 15px!important;
    border-radius: 3px;
    height: 28px;
}

/deep/.el-radio__input {
    display: none!important;
    white-space: nowrap;
    cursor: pointer;
    outline: 0;
    line-height: 1;
    vertical-align: middle;
}

/deep/.el-radio__label {
    padding-left: 0px;
}

/deep/.el-form-item__label {
    text-align: right;
    vertical-align: middle;
    float: left;
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    box-sizing: border-box;
    font-weight: 450;
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
    color: #000000;
}

/deep/.el-radio-group {
    font-size: 0;
    margin-top: 7px;
}

/deep/.el-radio {
    color: #000000;
    font-weight: 400;
    line-height: 1;
    cursor: pointer;
    white-space: nowrap;
    outline: 0;
    margin-right: 10px;
}
</style>