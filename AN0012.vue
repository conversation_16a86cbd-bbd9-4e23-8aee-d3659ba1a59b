<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            label-width="80px"
            style="overflow-y: auto"
            size="mini"
        >
            <el-form-item v-for="(op, index1) in options" :key="index1" :label="op.name" :props="op.field">
                <template v-slot:label>
                    <span>{{ op.name }}</span>
                    <el-checkbox v-model="op.checkAll" @change="handleCheckAllChange(op)" style="margin-left: 10px;">全选</el-checkbox>
                </template>
                <el-checkbox-group v-model="op.checkList" @change="handleCheckChange($event, op)">
                    <template v-for="(op2, index2) in op.options" >
                        <div v-if="op2.name">
                            <div style="margin-left: 25px;font-size: 14px;">{{ index2 + 1 }}.{{ op2.name }}：</div>
                            <el-checkbox v-for="(op3, index3) in op2.options" :key="`${index1}-${index2}-${index3}`" :label="op3.label" style="width: 100%;">&nbsp;&nbsp;&nbsp;{{ `${index3 + 1})` }} {{ op3.label }}{{  index2 < op2.options.length - 1 ? '；' : '' }}</el-checkbox>
                        </div>
                        <el-checkbox v-else :key="`${index1}-${index2}`" :label="op2.label" style="width: 100%;">{{ index2 + 1 }}.{{ op2.label }}{{  index2 < op.options.length - 1 ? '；' : '' }}</el-checkbox>
                    </template>

                </el-checkbox-group>
                <el-radio-group v-model="form[op.field]" style="pointer-events: none;">
                    <el-radio label="是" border>是</el-radio>
                    <el-radio label="否" border>否</el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="受试者是否满足入组标准？" prop="p1">
                <template v-slot:label>
                    <span>受试者是否满足入组标准？</span>
                    <el-checkbox v-model="isCheckAll" @change="handleCheckAllChange" style="margin-left: 10px;">全选</el-checkbox>
                </template>
                <div>
                    <el-checkbox label="1-1" >1.充分了解本试验的目和要求，自愿参加并签署书面知情同意书，并能按试验要求进行随访；</el-checkbox>
                    <el-checkbox label="1-2" >2.年龄≥50岁且无生育需求的男性或绝经后的女性患者；</el-checkbox>
                    <el-checkbox label="1-3" >3.根据美国风湿病学会（ACR）1987年修订的标准，或美国风湿病学会/欧洲抗风湿联盟（ACR/EULAR）2010年分类标准，诊断为RA的患者；</el-checkbox>
                    <el-checkbox label="1-4" >4.入组前使用除肿瘤坏死因子α抑制剂类以外的缓解病情抗风湿药DMARDs（包括传统合成DMARDs、生物DMARDs、靶向合成DMARDs）≥12周，且应答不佳 (DAS28-ESR>3.2)；</el-checkbox>
                    <div>
                        <div style="margin-left: 25px;">5.合并用药：</div>
                        <el-checkbox label="1-5-1">1）如果受试者使用非甾体抗炎药（NSAIDs）或其他镇痛药治疗，随机前需稳定剂量≥2周；</el-checkbox>
                        <el-checkbox label="1-5-2">2）如果受试者使用糖皮质激素，随机前需稳定剂量≥4周且剂量≤强的松10mg/日或相当剂量；随机前4周内不允许关节内（IA）或肌肉注射（IM）或静脉注射（IV）糖皮质激素；</el-checkbox>
                    </div>
                    <el-checkbox label="1-5">
                        <div>
                            5.合并用药：
                            <ul>
                                <li>1）如果受试者使用非甾体抗炎药（NSAIDs）或其他镇痛药治疗，随机前需稳定剂量≥2周；</li>
                                <li>2）如果受试者使用糖皮质激素，随机前需稳定剂量≥4周且剂量≤强的松10mg/日或相当剂量；随机前4周内不允许关节内（IA）或肌肉注射（IM）或静脉注射（IV）糖皮质激素；</li>
                            </ul>
                        </div>
                    </el-checkbox> -->
                    <!-- <div>
                        1.充分了解本试验的目和要求，自愿参加并签署书面知情同意书，并能按试验要求进行随访；
                    </div>
                    <div>2. 年龄≥50岁且无生育需求的男性或绝经后的女性患者；</div>
                    <div>
                        3.根据美国风湿病学会（ACR）1987年修订的标准，或美国风湿病学会/欧洲抗风湿联盟（ACR/EULAR）2010年分类标准，诊断为RA的患者；
                    </div>
                    <div>
                        4.入组前使用除肿瘤坏死因子α抑制剂类以外的缓解病情抗风湿药DMARDs（包括传统合成DMARDs、生物DMARDs、靶向合成DMARDs）≥12周，且应答不佳 (DAS28-ESR>3.2)；
                    </div>
                    <div>
                        5.合并用药：
                        <ul>
                            <li>1）如果受试者使用非甾体抗炎药（NSAIDs）或其他镇痛药治疗，随机前需稳定剂量≥2周；</li>
                            <li>2）如果受试者使用糖皮质激素，随机前需稳定剂量≥4周且剂量≤强的松10mg/日或相当剂量；随机前4周内不允许关节内（IA）或肌肉注射（IM）或静脉注射（IV）糖皮质激素；</li>
                        </ul>
                    </div>
                </div>

                <el-radio-group v-model="form.p1">
                    <el-radio label="是" border>是</el-radio>
                    <el-radio label="否" border>否</el-radio>
                </el-radio-group> -->
            <!-- </el-form-item>
            <el-form-item
                label="受试者是否在排除标准外？（符合以下任意一项即不能入组) "
                prop="p2"
            >
                <div>
                    <div>
                        1.受试者符合其他风湿疾病（如系统性红斑狼疮）的诊断标准；
                    </div>
                    <div>
                        2.已知对益赛普、甲氨蝶呤或雷公藤多苷的成分过敏；
                    </div>
                    <div>3.在过去3个月内有严重细菌感染者（如肺炎或肾盂肾炎，经抗生素治疗痊愈者除外）；</div>
                    <div>
                        4.有严重、慢性或反复发作细菌感染者（如肺炎反复发作及慢性支气管扩张）；
                    </div>
                    <div>
                        5.结核（TB）或隐匿性结核感染（符合下列条件之一）：
                        <ul>
                            <li>1）筛选时存在活动性TB或有活动性TB临床症状；</li>
                            <li>2）结核检测阳性（IGRA，T-spot，或QuantiFERON TB-Gold）；</li>
                            <li>3）筛选前3个月内影像学检查提示存在活动性TB征象；</li>
                        </ul>
                    </div>
                    <div>
                        6.随机前4周内出现以下任何实验室检查值者：
                        <ul>
                            <li>{{ '1）白细胞计数（WBC）<3.5×109/L；' }}</li>
                            <li>{{ '2）血红蛋白（HGB）<80.0g/L；' }}</li>
                            <li>{{ '3）血小板计数（PLT）<80×109/L；' }}</li>
                            <li>{{ '4）丙氨酸氨基转移酶（ALT）、天门冬氨酸氨基转移酶（AST）、血清肌酐（Cr）>正常值上限；' }}</li>
                            <li>5）乙型肝炎表面抗原(HBsAg)阳性；</li>
                            <li>6）丙型肝炎抗体（HCV）阳性；</li>
                            <li>7）人类免疫缺陷病毒抗体（HIV）阳性；</li>
                        </ul>
                    </div>
                    <div>
                        7.在随机前3个月内、研究期间、或末次研究给药后6个月内，使用或预计使用疫苗接种；
                    </div>
                    <div>
                        8.有严重的、进展性的或未控制的肾脏、肝脏、血液、胃肠、内分泌、肺部、心脏、神经、精神或脑部疾病；
                    </div>
                    <div>
                        9.既往5年内有恶性肿瘤病史；
                    </div>
                    <div>10.正在参与其它药物试验研究；</div>
                    <div>11.研究者认为存在任何临床或实验室检查异常有意义者或其他原因而不适合参加本临床研究者。</div>
                </div>
                <el-radio-group v-model="form.p2">
                    <el-radio label="是" border>是</el-radio>
                    <el-radio label="否" border>否</el-radio>
                </el-radio-group>
            </el-form-item> -->
            <el-form-item label="受试者是否符合符合纳入条件？" prop="p3">
                <el-radio-group v-model="form.p3" style="pointer-events: none;">
                    <el-radio label="是，确认符合入组条件，正式入组" border
                        >是，确认符合入组条件，正式入组</el-radio
                    >
                    <el-radio label="否，受试者不能参加本研究" border
                        >否，受试者不能参加本研究</el-radio
                    >
                </el-radio-group>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>

export default {
    name: "AN0012",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p1: '否',
                p2: '是',
                p3: '否，受试者不能参加本研究',
            },
            isCheckAll: false,
            options: [
                {
                    field: 'p1',
                    length: 6,
                    name: '受试者是否满足入组标准？',
                    checkList: [],
                    options: [
                        {label: '充分了解本试验的目和要求，自愿参加并签署书面知情同意书，并能按试验要求进行随访'},
                        {label: '年龄≥50岁且无生育需求的男性或绝经后的女性患者'},
                        {label: '根据美国风湿病学会（ACR）1987年修订的标准，或美国风湿病学会/欧洲抗风湿联盟（ACR/EULAR）2010年分类标准，诊断为RA的患者'},
                        {label: '入组前使用除肿瘤坏死因子α抑制剂类以外的缓解病情抗风湿药DMARDs（包括传统合成DMARDs、生物DMARDs、靶向合成DMARDs）≥12周，且应答不佳 (DAS28-ESR>3.2)'},
                        {name: '合并用药', options: [
                            {label: '如果受试者使用非甾体抗炎药（NSAIDs）或其他镇痛药治疗，随机前需稳定剂量≥2周'},
                            {label: '如果受试者使用糖皮质激素，随机前需稳定剂量≥4周且剂量≤强的松10mg/日或相当剂量；随机前4周内不允许关节内（IA）或肌肉注射（IM）或静脉注射（IV）糖皮质激素'}
                        ]}
                    ]
                },
                {
                    field: 'p2',
                    length: 19,
                    name: '受试者是否在排除标准外？（符合以下任意一项即不能入组) ',
                    checkList: [],
                    options: [
                        {label: '受试者符合其他风湿疾病（如系统性红斑狼疮）的诊断标准'},
                        {label: '已知对益赛普、甲氨蝶呤或雷公藤多苷的成分过敏'},
                        {label: '在过去3个月内有严重细菌感染者（如肺炎或肾盂肾炎，经抗生素治疗痊愈者除外）'},
                        {label: '有严重、慢性或反复发作细菌感染者（如肺炎反复发作及慢性支气管扩张）'},
                        {name: '结核（TB）或隐匿性结核感染（符合下列条件之一）', options: [
                            {label: '筛选时存在活动性TB或有活动性TB临床症状'},
                            {label: '结核检测阳性（IGRA，T-spot，或QuantiFERON TB-Gold）'},
                            {label: '筛选前3个月内影像学检查提示存在活动性TB征象'},
                        ]},
                        {name: '随机前4周内出现以下任何实验室检查值者', options: [
                            {label: '白细胞计数（WBC）<3.5×109/L'},
                            {label: '血红蛋白（HGB）<80.0g/L'},
                            {label: '血小板计数（PLT）<80×109/L'},
                            {label: '丙氨酸氨基转移酶（ALT）、天门冬氨酸氨基转移酶（AST）、血清肌酐（Cr）>正常值上限'},
                            {label: '乙型肝炎表面抗原(HBsAg)阳性'},
                            {label: '丙型肝炎抗体（HCV）阳性'},
                            {label: '人类免疫缺陷病毒抗体（HIV）阳性'},
                        ]},
                        {label: '在随机前3个月内、研究期间、或末次研究给药后6个月内，使用或预计使用疫苗接种'},
                        {label: '有严重的、进展性的或未控制的肾脏、肝脏、血液、胃肠、内分泌、肺部、心脏、神经、精神或脑部疾病'},
                        {label: '既往5年内有恶性肿瘤病史'},
                        {label: '正在参与其它药物试验研究'},
                        {label: '研究者认为存在任何临床或实验室检查异常有意义者或其他原因而不适合参加本临床研究者'},
                    ]
                },
            ],
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "受试者是否满足入组标准？",
                        trigger: "change",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "受试者是否在排除标准外？",
                        trigger: "change",
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "受试者是否符合符合纳入条件？",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        handleCheckAllChange(option) {
            const check = (list) => {
                list.forEach(item => {
                    if (item.name && item.options) {
                        check(item.options)
                    } else {
                        option.checkList.push(item.label)
                    }
                })
            }
            option.checkList = []
            if (option.checkAll) {
                check(option.options)
                this.$set(this.form, option.field, option.field == 'p1' ? '是' : '否')
            } else {
                this.$set(this.form, option.field, option.field == 'p1' ? '否' : '是')
            }
            this.handleSetP3()
        },
        handleCheckChange(val, option) {
            option.checkAll = val.length == option.length
            if (option.checkAll) {
                this.$set(this.form, option.field, option.field == 'p1' ? '是' : '否')
            } else {
                if (option.field == 'p2') {
                    if (val.length) {
                        this.$set(this.form, option.field, '否')
                    } else {
                        this.$set(this.form, option.field, '是')
                    }

                } else {
                    this.$set(this.form, option.field, '否')
                }

            }
            this.handleSetP3()
        },
        handleSetP3() {
            if (this.form.p1 == '是' && this.form.p2 == '是' ) {
                this.$set(this.form, 'p3', '是，确认符合入组条件，正式入组')
            } else {
                this.$set(this.form, 'p3', '否，受试者不能参加本研究')
            }
        },
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
