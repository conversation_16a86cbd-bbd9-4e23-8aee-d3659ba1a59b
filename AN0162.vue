<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <!-- 1、cTNM分期 -->
      <el-form-item prop="ctnm" label="1、cTNM分期">
        <el-input
          v-model="form.ctnm"
          placeholder="请输入cTNM分期"
          maxlength="50"
          show-word-limit
          style="width: 300px"
        />
      </el-form-item>

      <!-- 2、临床T分期 (cT) -->
      <el-form-item label="2、临床T分期 (cT)">
        <!-- （1）影像学评估（MRI、PSMA PET） -->
        <el-form-item prop="ctImageAssessment" label="（1）影像学评估（MRI、PSMA PET）">
          <!-- 侵犯邻近器官 -->
          <el-form-item prop="ctInvasion" label="侵犯邻近器官">
            <el-radio-group v-model="form.ctInvasion">
              <el-radio label="有" border/>
              <el-radio label="无" border/>
            </el-radio-group>
          </el-form-item>

          <!-- T分期建议 -->
          <el-form-item prop="ctStageRecommendation" label="T分期建议">
            <el-input
              v-model="form.ctStageRecommendation"
              placeholder="请输入T分期建议"
              maxlength="100"
              show-word-limit
              style="width: 300px"
            />
          </el-form-item>
        </el-form-item>

        <!-- （2）前列腺穿刺活检病理评估 -->
        <el-form-item prop="ctBiopsyAssessment" label="（2）前列腺穿刺活检病理评估">
          <el-input
            v-model="form.ctBiopsyAssessment"
            type="textarea"
            :rows="3"
            placeholder="请输入前列腺穿刺活检病理评估"
            maxlength="500"
            show-word-limit
            style="width: 400px"
          />
        </el-form-item>
      </el-form-item>

      <!-- 3、临床N分期 (cN) -->
      <el-form-item label="3、临床N分期 (cN)">
        <!-- （1）影像学评估（CT/MRI） -->
        <el-form-item prop="cnImageAssessment" label="（1）影像学评估（CT/MRI）">
          <!-- 盆腔淋巴结肿大 -->
          <el-form-item prop="cnLymphNodeEnlargement" label="盆腔淋巴结肿大">
            <el-radio-group v-model="form.cnLymphNodeEnlargement">
              <el-radio label="有" border/>
              <el-radio label="无" border/>
            </el-radio-group>
          </el-form-item>

          <!-- 肿大淋巴结部位 -->
          <el-form-item v-if="form.cnLymphNodeEnlargement === '有'" prop="cnLymphNodeLocation" label="肿大淋巴结部位（多选）">
            <el-checkbox-group v-model="form.cnLymphNodeLocation">
              <el-checkbox label="髂内" border />
              <el-checkbox label="髂外" border />
              <el-checkbox label="闭孔" border />
              <el-checkbox label="髂总" border />
              <el-checkbox label="骶前" border />
            </el-checkbox-group>
          </el-form-item>

          <!-- 最大淋巴结直径 -->
          <el-form-item v-if="form.cnLymphNodeEnlargement === '有'" prop="cnMaxLymphNodeDiameter" label="最大淋巴结直径">
            <el-input
              v-model="form.cnMaxLymphNodeDiameter"
              placeholder="请输入最大淋巴结直径"
              style="width: 200px"
              @input="handleCnMaxLymphNodeDiameterInput"
              @keypress="handleNumberKeypress"
            />
            <span style="margin-left: 5px;">cm</span>
          </el-form-item>

          <!-- 淋巴结形态 -->
          <el-form-item v-if="form.cnLymphNodeEnlargement === '有'" prop="cnLymphNodeMorphology" label="淋巴结形态">
            <el-input
              v-model="form.cnLymphNodeMorphology"
              placeholder="请输入淋巴结形态描述"
              maxlength="200"
              show-word-limit
              style="width: 300px"
            />
          </el-form-item>
        </el-form-item>
      </el-form-item>

      <!-- 4、临床M分期 (cM) -->
      <el-form-item label="4、临床M分期 (cM)">
        <!-- （1）远处转移 -->
        <el-form-item prop="cmDistantMetastasis" label="（1）远处转移">
          <el-radio-group v-model="form.cmDistantMetastasis">
            <el-radio label="有" border/>
            <el-radio label="无" border/>
          </el-radio-group>
        </el-form-item>

        <!-- （2）转移部位 -->
        <el-form-item v-if="form.cmDistantMetastasis === '有'" prop="cmMetastasisLocation" label="（2）转移部位（如果存在）（多选）">
          <el-checkbox-group v-model="form.cmMetastasisLocation">
            <el-checkbox label="骨" border />
            <el-checkbox label="肝" border />
            <el-checkbox label="肺" border />
            <el-checkbox label="肾上腺" border />
            <el-checkbox label="非区域淋巴结（如腹膜后、骶前）" border />
            <el-checkbox label="其他" border />
          </el-checkbox-group>
          <el-input
            v-if="form.cmMetastasisLocation.includes('其他')"
            v-model="form.cmMetastasisLocationOther"
            placeholder="请填写其他转移部位"
            maxlength="100"
            show-word-limit
            class="cm-location-other"
          />
        </el-form-item>

        <!-- （3）评估方式 -->
        <el-form-item v-if="form.cmDistantMetastasis === '有'" prop="cmAssessmentMethod" label="（3）评估方式（多选）">
          <el-checkbox-group v-model="form.cmAssessmentMethod">
            <el-checkbox label="胸部CT" border />
            <el-checkbox label="腹部CT" border />
            <el-checkbox label="骨扫描" border />
            <el-checkbox label="PSMA PET-CT" border />
            <el-checkbox label="SPECT" border />
          </el-checkbox-group>
        </el-form-item>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0162',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
      // 初始化所有可能的字段
      const fields = [
        'cnLymphNodeLocation', 'cmMetastasisLocation', 'cmMetastasisLocationOther', 'cmAssessmentMethod'
      ];

      fields.forEach(field => {
        if (this.form[field] === undefined) {
          if (field.includes('Location') && !field.includes('Other') || field.includes('Method')) {
            this.$set(this.form, field, []);
          } else {
            this.$set(this.form, field, '');
          }
        }
      });
    }
  },
  data() {
    // 验证函数
    const validateCnLymphNodeLocation = (rule, value, callback) => {
      if (this.form.cnLymphNodeEnlargement === '有' && (!value || value.length === 0)) {
        callback(new Error('请选择肿大淋巴结部位'));
      } else {
        callback();
      }
    };

    const validateCnMaxLymphNodeDiameter = (rule, value, callback) => {
      if (this.form.cnLymphNodeEnlargement === '有') {
        if (!value) {
          callback(new Error('请输入最大淋巴结直径'));
        } else if (!/^\d+(\.\d+)?$/.test(value)) {
          callback(new Error('请输入有效的数字'));
        } else if (parseFloat(value) <= 0) {
          callback(new Error('最大淋巴结直径必须大于0'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };

    const validateCnLymphNodeMorphology = (rule, value, callback) => {
      if (this.form.cnLymphNodeEnlargement === '有' && !value) {
        callback(new Error('请输入淋巴结形态描述'));
      } else {
        callback();
      }
    };

    const validateCmMetastasisLocation = (rule, value, callback) => {
      if (this.form.cmDistantMetastasis === '有' && (!value || value.length === 0)) {
        callback(new Error('请选择转移部位'));
      } else {
        callback();
      }
    };

    const validateCmMetastasisLocationOther = (rule, value, callback) => {
      if (this.form.cmDistantMetastasis === '有' && this.form.cmMetastasisLocation.includes('其他') && !value) {
        callback(new Error('请填写其他转移部位'));
      } else {
        callback();
      }
    };

    const validateCmAssessmentMethod = (rule, value, callback) => {
      if (this.form.cmDistantMetastasis === '有' && (!value || value.length === 0)) {
        callback(new Error('请选择评估方式'));
      } else {
        callback();
      }
    };

    return {
      form: {
        ctnm: '',
        ctInvasion: undefined,
        ctStageRecommendation: '',
        ctBiopsyAssessment: '',
        cnLymphNodeEnlargement: undefined,
        cnLymphNodeLocation: [],
        cnMaxLymphNodeDiameter: '',
        cnLymphNodeMorphology: '',
        cmDistantMetastasis: undefined,
        cmMetastasisLocation: [],
        cmMetastasisLocationOther: '',
        cmAssessmentMethod: [],
      },
      // 表单校验
      rules: {
        ctnm: [{ required: true, message: '请输入cTNM分期', trigger: 'blur' }],
        ctInvasion: [{ required: true, message: '必须选择', trigger: 'blur' }],
        ctStageRecommendation: [{ required: true, message: '请输入T分期建议', trigger: 'blur' }],
        ctBiopsyAssessment: [{ required: true, message: '请输入前列腺穿刺活检病理评估', trigger: 'blur' }],
        cnLymphNodeEnlargement: [{ required: true, message: '必须选择', trigger: 'blur' }],
        cnLymphNodeLocation: [{ validator: validateCnLymphNodeLocation, trigger: 'change' }],
        cnMaxLymphNodeDiameter: [{ validator: validateCnMaxLymphNodeDiameter, trigger: 'blur' }],
        cnLymphNodeMorphology: [{ validator: validateCnLymphNodeMorphology, trigger: 'blur' }],
        cmDistantMetastasis: [{ required: true, message: '必须选择', trigger: 'blur' }],
        cmMetastasisLocation: [{ validator: validateCmMetastasisLocation, trigger: 'change' }],
        cmMetastasisLocationOther: [{ validator: validateCmMetastasisLocationOther, trigger: 'blur' }],
        cmAssessmentMethod: [{ validator: validateCmAssessmentMethod, trigger: 'change' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
    /** 处理最大淋巴结直径输入，只允许数字和小数点 */
    handleCnMaxLymphNodeDiameterInput(value) {
      // 只保留数字和小数点
      const numericValue = value.replace(/[^\d.]/g, '');
      // 确保只有一个小数点
      const parts = numericValue.split('.');
      if (parts.length > 2) {
        this.form.cnMaxLymphNodeDiameter = parts[0] + '.' + parts.slice(1).join('');
      } else {
        this.form.cnMaxLymphNodeDiameter = numericValue;
      }
    },
    /** 处理按键事件，只允许数字、小数点、退格和删除键 */
    handleNumberKeypress(event) {
      const charCode = event.which ? event.which : event.keyCode;
      // 允许数字(48-57)、小数点(46)、退格(8)、删除(46)、方向键等控制键
      if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
        event.preventDefault();
      }
      // 如果已经有小数点，不允许再输入小数点
      if (charCode === 46 && event.target.value.indexOf('.') !== -1) {
        event.preventDefault();
      }
    },
  },
  watch: {
    'form.cnLymphNodeEnlargement'(val) {
      if (val !== '有') {
        this.form.cnLymphNodeLocation = [];
        this.form.cnMaxLymphNodeDiameter = '';
        this.form.cnLymphNodeMorphology = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['cnLymphNodeLocation', 'cnMaxLymphNodeDiameter', 'cnLymphNodeMorphology']);
        }
      }
    },
    'form.cmDistantMetastasis'(val) {
      if (val !== '有') {
        this.form.cmMetastasisLocation = [];
        this.form.cmMetastasisLocationOther = '';
        this.form.cmAssessmentMethod = [];
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['cmMetastasisLocation', 'cmMetastasisLocationOther', 'cmAssessmentMethod']);
        }
      }
    },
    'form.cmMetastasisLocation'(val) {
      if (!val.includes('其他')) {
        this.form.cmMetastasisLocationOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('cmMetastasisLocationOther');
        }
      }
    },
  },
};
export const formConfig = {
  fieldList: [ {
      key:"ctnm",
      label:"1、cTNM分期"
    }, {
      key:"ctInvasion",
      label:"2、临床T分期 (cT) - 侵犯邻近器官（有/无）"
    }, {
      key:"cnLymphNodeEnlargement",
      label:"3、临床N分期 (cN) - 盆腔淋巴结肿大（有/无）"
    }, {
      key:"cmDistantMetastasis",
      label:"4、临床M分期 (cM) - 远处转移（有/无）"
    } ],
};
</script>
<style scoped>
::v-deep .el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

::v-deep .el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-radio__label {
  padding-left: 0px;
}

::v-deep .el-checkbox--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
  margin-right: 10px;
  margin-bottom: 10px;
}

::v-deep .el-checkbox__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-checkbox__label {
  padding-left: 0px;
}

::v-deep .el-form-item__label {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

::v-deep .el-radio-group {
  font-size: 0;
}

::v-deep .el-checkbox-group {
  font-size: 0;
}

::v-deep .el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

::v-deep .el-checkbox {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

.cm-location-other {
  width: 300px;
  margin-left: 10px;
  margin-top: 10px;
}
</style>
