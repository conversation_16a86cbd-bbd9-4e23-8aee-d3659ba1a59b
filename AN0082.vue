<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <el-form-item prop="p1" label="检查部位">
        <el-input type="textarea" v-model="form.p1" :rows="1" maxlength="50" show-word-limit></el-input>
      </el-form-item>
      <el-form-item prop="p2" label="检查所见">
        <el-input type="textarea" v-model="form.p2" :rows="5" maxlength="1000" show-word-limit></el-input>
      </el-form-item>
      <el-form-item prop="p3" label="检查意见">
        <el-input type="textarea" v-model="form.p3" :rows="5" maxlength="1000" show-word-limit></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0082',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '检查部位必须输入', trigger: 'blur' }],
        p2: [{ required: true, message: '检查所见必须输入', trigger: 'blur' }],
        p3: [{ required: true, message: '检查意见必须输入', trigger: 'blur' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
<style scoped>
/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item__label {
  text-align: right;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>