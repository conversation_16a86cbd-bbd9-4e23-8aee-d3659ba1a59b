<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <!-- 1、是否存在骨转移 -->
      <el-form-item prop="p1" label="1、是否存在骨转移">
        <el-radio-group v-model="form.p1">
          <el-radio label="有" border/>
          <el-radio label="无" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 转移灶部位 -->
      <el-form-item v-if="form.p1 === '有'" prop="p1Location" label="（1）转移灶部位">
        <el-checkbox-group v-model="form.p1Location">
          <el-checkbox label="脊柱（颈椎）" border />
          <el-checkbox label="脊柱（胸椎）" border />
          <el-checkbox label="脊柱（腰椎）" border />
          <el-checkbox label="脊柱（骶椎）" border />
          <el-checkbox label="骨盆（髂骨）" border />
          <el-checkbox label="骨盆（坐骨）" border />
          <el-checkbox label="骨盆（耻骨）" border />
          <el-checkbox label="肋骨" border />
          <el-checkbox label="股骨（股骨头）" border />
          <el-checkbox label="股骨（股骨颈）" border />
          <el-checkbox label="股骨（股骨干）" border />
          <el-checkbox label="颅骨" border />
          <el-checkbox label="肩胛骨" border />
          <el-checkbox label="其他部位" border />
        </el-checkbox-group>
        <el-input
          v-if="form.p1Location.includes('其他部位')"
          v-model="form.p1LocationOther"
          placeholder="请填写其他部位"
          maxlength="50"
          show-word-limit
          class="p1-location-other"
        />
      </el-form-item>

      <!-- 转移灶数量 -->
      <el-form-item v-if="form.p1 === '有'" prop="p1Number" label="（2）转移灶数量">
        <el-radio-group v-model="form.p1Number" class="p1-number-group">
          <el-radio label="单发病灶" border />
          <el-radio label="多发病灶（≤5个）" border />
          <el-radio label="多发病灶（＞5个）" border />
          <el-radio label="弥漫性分布" border />
        </el-radio-group>
      </el-form-item>

      <!-- 转移灶放射性表现 -->
      <el-form-item v-if="form.p1 === '有'" prop="p1RadioFeature" label="（3）转移灶放射性表现">
        <el-checkbox-group v-model="form.p1RadioFeature">
          <el-checkbox label="放射性浓聚灶" border />
          <el-checkbox label="放射性稀疏缺损区" border />
          <el-checkbox label="混合性病灶" border />
          <el-checkbox label="其他表现" border />
        </el-checkbox-group>
        <el-input
          v-if="form.p1RadioFeature.includes('其他表现')"
          v-model="form.p1RadioFeatureOther"
          placeholder="请填写其他表现"
          maxlength="50"
          show-word-limit
          class="p1-radio-other"
        />
      </el-form-item>

      <!-- 是否合并病理性骨折 -->
      <el-form-item v-if="form.p1 === '有'" prop="p1Fracture" label="（4）是否合并病理性骨折">
        <el-radio-group v-model="form.p1Fracture" class="p1-fracture-group">
          <el-radio label="有" border />
          <el-radio label="无" border />
        </el-radio-group>
        <el-input
          v-if="form.p1Fracture === '有'"
          v-model="form.p1FractureLocation"
          placeholder="请填写骨折部位"
          maxlength="50"
          show-word-limit
          class="p1-fracture-location"
        />
      </el-form-item>

      <!-- 是否合并骨外异常摄取 -->
      <el-form-item v-if="form.p1 === '有'" prop="p1ExtraUptake" label="（5）是否合并骨外异常摄取">
        <el-radio-group v-model="form.p1ExtraUptake" class="p1-extra-group">
          <el-radio label="有" border />
          <el-radio label="无" border />
        </el-radio-group>
        <el-input
          v-if="form.p1ExtraUptake === '有'"
          v-model="form.p1ExtraUptakeLocation"
          placeholder="请填写部位"
          maxlength="50"
          show-word-limit
          class="p1-extra-location"
        />
      </el-form-item>

      <!-- 2、与既往检查对比 -->
      <el-form-item prop="p2" label="2、与既往检查对比（若有多次骨扫描）">
        <el-radio-group v-model="form.p2">
          <el-radio label="有既往检查" border/>
          <el-radio label="无既往检查" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 病灶变化 -->
      <el-form-item v-if="form.p2 === '有既往检查'" prop="p2Change" label="（1）病灶变化">
        <el-checkbox-group v-model="form.p2Change">
          <el-checkbox label="进展（新增病灶）" border />
          <el-checkbox label="进展（原有病灶范围扩大）" border />
          <el-checkbox label="进展（放射性浓聚增强）" border />
          <el-checkbox label="稳定（病灶数量、大小、放射性强度无明显变化）" border />
          <el-checkbox label="缓解（原有病灶缩小）" border />
          <el-checkbox label="缓解（放射性浓聚减弱）" border />
          <el-checkbox label="缓解（病灶消失）" border />
          <el-checkbox label="其他" border />
        </el-checkbox-group>
        <el-input
          v-if="form.p2Change.includes('其他')"
          v-model="form.p2ChangeOther"
          placeholder="请填写其他变化"
          maxlength="50"
          show-word-limit
          class="p2-change-other"
        />
      </el-form-item>

      <!-- 变化原因推测 -->
      <el-form-item v-if="form.p2 === '有既往检查'" prop="p2Reason" label="（2）变化原因推测">
        <el-radio-group v-model="form.p2Reason" class="p2-reason-group">
          <el-radio label="治疗起效（放疗）" border />
          <el-radio label="治疗起效（靶向治疗）" border />
          <el-radio label="病情进展" border />
          <el-radio label="其他" border />
        </el-radio-group>
        <el-input
          v-if="form.p2Reason === '其他'"
          v-model="form.p2ReasonOther"
          placeholder="请填写其他原因"
          maxlength="50"
          show-word-limit
          class="p2-reason-other"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0160',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
      // 初始化所有可能的字段
      const fields = [
        'p1Location', 'p1LocationOther', 'p1Number', 'p1RadioFeature', 'p1RadioFeatureOther',
        'p1Fracture', 'p1FractureLocation', 'p1ExtraUptake', 'p1ExtraUptakeLocation',
        'p2Change', 'p2ChangeOther', 'p2Reason', 'p2ReasonOther'
      ];

      fields.forEach(field => {
        if (this.form[field] === undefined) {
          if (field.includes('Location') && !field.includes('Other') || field.includes('Change') || field.includes('RadioFeature')) {
            this.$set(this.form, field, []);
          } else {
            this.$set(this.form, field, '');
          }
        }
      });
    }
  },
  data() {
    // 验证函数
    const validateP1Location = (rule, value, callback) => {
      if (this.form.p1 === '有' && (!value || value.length === 0)) {
        callback(new Error('请选择转移灶部位'));
      } else {
        callback();
      }
    };

    const validateP1LocationOther = (rule, value, callback) => {
      if (this.form.p1 === '有' && this.form.p1Location.includes('其他部位') && !value) {
        callback(new Error('请填写其他部位'));
      } else {
        callback();
      }
    };

    const validateP1Number = (rule, value, callback) => {
      if (this.form.p1 === '有' && !value) {
        callback(new Error('请选择转移灶数量'));
      } else {
        callback();
      }
    };

    const validateP1RadioFeature = (rule, value, callback) => {
      if (this.form.p1 === '有' && (!value || value.length === 0)) {
        callback(new Error('请选择转移灶放射性表现'));
      } else {
        callback();
      }
    };

    const validateP1RadioFeatureOther = (rule, value, callback) => {
      if (this.form.p1 === '有' && this.form.p1RadioFeature.includes('其他表现') && !value) {
        callback(new Error('请填写其他表现'));
      } else {
        callback();
      }
    };

    const validateP1Fracture = (rule, value, callback) => {
      if (this.form.p1 === '有' && !value) {
        callback(new Error('请选择是否合并病理性骨折'));
      } else {
        callback();
      }
    };

    const validateP1FractureLocation = (rule, value, callback) => {
      if (this.form.p1 === '有' && this.form.p1Fracture === '有' && !value) {
        callback(new Error('请填写骨折部位'));
      } else {
        callback();
      }
    };

    const validateP1ExtraUptake = (rule, value, callback) => {
      if (this.form.p1 === '有' && !value) {
        callback(new Error('请选择是否合并骨外异常摄取'));
      } else {
        callback();
      }
    };

    const validateP1ExtraUptakeLocation = (rule, value, callback) => {
      if (this.form.p1 === '有' && this.form.p1ExtraUptake === '有' && !value) {
        callback(new Error('请填写部位'));
      } else {
        callback();
      }
    };

    const validateP2Change = (rule, value, callback) => {
      if (this.form.p2 === '有既往检查' && (!value || value.length === 0)) {
        callback(new Error('请选择病灶变化'));
      } else {
        callback();
      }
    };

    const validateP2ChangeOther = (rule, value, callback) => {
      if (this.form.p2 === '有既往检查' && this.form.p2Change.includes('其他') && !value) {
        callback(new Error('请填写其他变化'));
      } else {
        callback();
      }
    };

    const validateP2Reason = (rule, value, callback) => {
      if (this.form.p2 === '有既往检查' && !value) {
        callback(new Error('请选择变化原因推测'));
      } else {
        callback();
      }
    };

    const validateP2ReasonOther = (rule, value, callback) => {
      if (this.form.p2 === '有既往检查' && this.form.p2Reason === '其他' && !value) {
        callback(new Error('请填写其他原因'));
      } else {
        callback();
      }
    };

    return {
      form: {
        p1: undefined,
        p1Location: [],
        p1LocationOther: '',
        p1Number: '',
        p1RadioFeature: [],
        p1RadioFeatureOther: '',
        p1Fracture: '',
        p1FractureLocation: '',
        p1ExtraUptake: '',
        p1ExtraUptakeLocation: '',
        p2: undefined,
        p2Change: [],
        p2ChangeOther: '',
        p2Reason: '',
        p2ReasonOther: '',
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p1Location: [{ validator: validateP1Location, trigger: 'change' }],
        p1LocationOther: [{ validator: validateP1LocationOther, trigger: 'blur' }],
        p1Number: [{ validator: validateP1Number, trigger: 'change' }],
        p1RadioFeature: [{ validator: validateP1RadioFeature, trigger: 'change' }],
        p1RadioFeatureOther: [{ validator: validateP1RadioFeatureOther, trigger: 'blur' }],
        p1Fracture: [{ validator: validateP1Fracture, trigger: 'change' }],
        p1FractureLocation: [{ validator: validateP1FractureLocation, trigger: 'blur' }],
        p1ExtraUptake: [{ validator: validateP1ExtraUptake, trigger: 'change' }],
        p1ExtraUptakeLocation: [{ validator: validateP1ExtraUptakeLocation, trigger: 'blur' }],
        p2: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p2Change: [{ validator: validateP2Change, trigger: 'change' }],
        p2ChangeOther: [{ validator: validateP2ChangeOther, trigger: 'blur' }],
        p2Reason: [{ validator: validateP2Reason, trigger: 'change' }],
        p2ReasonOther: [{ validator: validateP2ReasonOther, trigger: 'blur' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
  watch: {
    'form.p1'(val) {
      if (val !== '有') {
        this.form.p1Location = [];
        this.form.p1LocationOther = '';
        this.form.p1Number = '';
        this.form.p1RadioFeature = [];
        this.form.p1RadioFeatureOther = '';
        this.form.p1Fracture = '';
        this.form.p1FractureLocation = '';
        this.form.p1ExtraUptake = '';
        this.form.p1ExtraUptakeLocation = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p1Location', 'p1LocationOther', 'p1Number', 'p1RadioFeature', 'p1RadioFeatureOther', 'p1Fracture', 'p1FractureLocation', 'p1ExtraUptake', 'p1ExtraUptakeLocation']);
        }
      }
    },
    'form.p1Location'(val) {
      if (!val.includes('其他部位')) {
        this.form.p1LocationOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p1LocationOther');
        }
      }
    },
    'form.p1RadioFeature'(val) {
      if (!val.includes('其他表现')) {
        this.form.p1RadioFeatureOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p1RadioFeatureOther');
        }
      }
    },
    'form.p1Fracture'(val) {
      if (val !== '有') {
        this.form.p1FractureLocation = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p1FractureLocation');
        }
      }
    },
    'form.p1ExtraUptake'(val) {
      if (val !== '有') {
        this.form.p1ExtraUptakeLocation = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p1ExtraUptakeLocation');
        }
      }
    },
    'form.p2'(val) {
      if (val !== '有既往检查') {
        this.form.p2Change = [];
        this.form.p2ChangeOther = '';
        this.form.p2Reason = '';
        this.form.p2ReasonOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p2Change', 'p2ChangeOther', 'p2Reason', 'p2ReasonOther']);
        }
      }
    },
    'form.p2Change'(val) {
      if (!val.includes('其他')) {
        this.form.p2ChangeOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p2ChangeOther');
        }
      }
    },
    'form.p2Reason'(val) {
      if (val !== '其他') {
        this.form.p2ReasonOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p2ReasonOther');
        }
      }
    },
  },
};
export const formConfig = {
  fieldList: [ {
      key:"p1",
      label:"1、是否存在骨转移（有 / 无）"
    }, {
      key:"p2",
      label:"2、与既往检查对比（有既往检查 / 无既往检查）"
    } ],
};
</script>
<style scoped>
::v-deep .el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

::v-deep .el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-radio__label {
  padding-left: 0px;
}

::v-deep .el-checkbox--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
  margin-right: 10px;
  margin-bottom: 10px;
}

::v-deep .el-checkbox__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-checkbox__label {
  padding-left: 0px;
}

::v-deep .el-form-item__label {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

::v-deep .el-radio-group {
  font-size: 0;
}

::v-deep .el-checkbox-group {
  font-size: 0;
}

::v-deep .el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

::v-deep .el-checkbox {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

.p1-location-other,
.p1-radio-other,
.p1-fracture-location,
.p1-extra-location,
.p2-change-other,
.p2-reason-other {
  width: 300px;
  margin-left: 10px;
  margin-top: 10px;
}

.p1-number-group,
.p1-fracture-group,
.p1-extra-group,
.p2-reason-group {
  display: inline-flex;
  align-items: center;
}
</style>
