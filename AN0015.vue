<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            label-width="80px"
            style="overflow-y: auto"
            size="mini"
        >
            <el-form-item label="1、患者是否有既往疾病史？" prop="p1">
                <el-radio-group v-model="form.p1" @change="handleChangeP1">
                    <el-radio label="有" border>有</el-radio>
                    <el-radio label="无" border>无</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="1.1、既往疾病史的详细描述" prop="p2">
                <el-table
                    :data="form.p2"
                    border
                    style="width: 100%"
                    size="mini"
                >
                    <el-table-column
                        type="index"
                        label="序号"
                        width="50"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="p1"
                        label="诊断名"
                        header-align="center"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p1"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入诊断名"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p2"
                        label="开始日期"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                                v-model="scope.row.p2"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期"
                                style="width: 130px"
                            ></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p3"
                        label="结束日期"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                                v-model="scope.row.p3"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期"
                                style="width: 130px"
                            ></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p4"
                        label="对类风湿性关节炎治疗的影响"
                        width="150"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-radio-group v-model="scope.row.p4" size="mini">
                                <el-radio label="有" />
                                <el-radio label="无" />
                            </el-radio-group>
                        </template>
                    </el-table-column>
                    <el-table-column width="40" header-align="center">
                        <template slot="header" slot-scope="scope">
                            <i
                                class="el-icon-circle-plus"
                                style="
                                    color: #409eff;
                                    cursor: pointer;
                                    font-size: 18px;
                                "
                                @click="add('p2')"
                            ></i>
                        </template>

                        <template slot-scope="scope">
                            <i
                                class="el-icon-remove"
                                style="
                                    color: red;
                                    cursor: pointer;
                                    font-size: 18px;
                                "
                                @click="del(scope.$index, 'p2')"
                            ></i>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>

            <el-form-item
                label="2、患者是否有有重要的外科手术（操作）史？"
                prop="p3"
            >
                <el-radio-group v-model="form.p3" @change="handleChangeP3">
                    <el-radio label="有" border>有</el-radio>
                    <el-radio label="无" border>无</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="2.1、既往疾病史的详细描述" prop="p4">
                <el-table
                    :data="form.p4"
                    border
                    style="width: 100%"
                    size="mini"
                >
                    <el-table-column
                        type="index"
                        label="序号"
                        width="50"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="p1"
                        label="手术（操作）史名称"
                        header-align="center"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p1"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入手术（操作）史名称"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p2"
                        label="手术（操作）年月"
                        width="200"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                                v-model="scope.row.p2"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期"
                                style="width: 130px"
                            ></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column width="40" header-align="center">
                        <template slot="header" slot-scope="scope">
                            <i
                                class="el-icon-circle-plus"
                                style="
                                    color: #409eff;
                                    cursor: pointer;
                                    font-size: 18px;
                                "
                                @click="add('p4')"
                            ></i>
                        </template>

                        <template slot-scope="scope">
                            <i
                                class="el-icon-remove"
                                style="
                                    color: red;
                                    cursor: pointer;
                                    font-size: 18px;
                                "
                                @click="del(scope.$index, 'p4')"
                            ></i>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>

            <el-form-item
                label="3、至今3个月内患者使用非类风湿性关节炎治疗的药物？"
                prop="p5"
            >
                <el-radio-group v-model="form.p5" @change="handleChangeP5">
                    <el-radio label="有" border>有</el-radio>
                    <el-radio label="无" border>无</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item
                label="3.1、非类风湿性关节炎治疗药物的详细描述"
                prop="p6"
            >
                <el-table
                    :data="form.p6"
                    border
                    style="width: 100%"
                    size="mini"
                >
                    <el-table-column
                        type="index"
                        label="序号"
                        width="50"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="p1"
                        label="药品名称"
                        header-align="center"
                        width="200"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p1"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入药品名称"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p2"
                        label="用药指征"
                        header-align="center"
                        width="200"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p2"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入用药指征"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p3"
                        label="用法用量"
                        header-align="center"
                        width="200"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.p3"
                                size="mini"
                                maxlength="50"
                                placeholder="请输入用法用量"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p4"
                        label="开始日期"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                                v-model="scope.row.p4"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期"
                                style="width: 130px"
                            ></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p5"
                        label="结束日期"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-date-picker
                                v-model="scope.row.p5"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期"
                                style="width: 130px"
                            ></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="p6"
                        label="是否继续"
                        width="150"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-radio-group v-model="scope.row.p6" size="mini">
                                <el-radio label="是" />
                                <el-radio label="否" />
                            </el-radio-group>
                        </template>
                    </el-table-column>
                    <el-table-column width="40" header-align="center">
                        <template slot="header" slot-scope="scope">
                            <i
                                class="el-icon-circle-plus"
                                style="
                                    color: #409eff;
                                    cursor: pointer;
                                    font-size: 18px;
                                "
                                @click="add('p6')"
                            ></i>
                        </template>

                        <template slot-scope="scope">
                            <i
                                class="el-icon-remove"
                                style="
                                    color: red;
                                    cursor: pointer;
                                    font-size: 18px;
                                "
                                @click="del(scope.$index, 'p6')"
                            ></i>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0015",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p2: [],
                p4: [],
                p6: [],
            },
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "患者是否有既往疾病史？",
                        trigger: "change",
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "患者是否有有重要的外科手术（操作）史？",
                        trigger: "change",
                    },
                ],
                p5: [
                    {
                        required: true,
                        message:
                            "至今3个月内患者使用非类风湿性关节炎治疗的药物？",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        add(key) {
            switch (key) {
                case "p2":
                    this.form[key].push({
                        p1: "",
                        p2: "",
                        p3: "",
                        p4: "有",
                    });
                    break;
                case "p4":
                    this.form[key].push({
                        p1: "",
                        p2: "",
                    });
                    break;
                case "p6":
                    this.form[key].push({
                        p1: "",
                        p2: "",
                        p3: "",
                        p4: "",
                        p5: "",
                        p6: "是",
                    });
                    break;
                default:
                    break;
            }
        },
        del(index, key) {
            this.form[key].splice(index, 1);
        },
        handleChangeP1(value) {
            if (value == "有") {
                this.add("p2");
            }
        },
        handleChangeP3(value) {
            if (value == "有") {
                this.add("p4");
            }
        },
        handleChangeP5(value) {
            if (value == "有") {
                this.add("p6");
            }
        },
    },
};
</script>
<style scoped>
.mb-10 {
    margin-bottom: 10px;
}
::v-deep .el-radio--mini.is-bordered .el-radio__inner {
    display: none !important;
}

::v-deep .el-radio {
    margin-right: 10px;
}
</style>
