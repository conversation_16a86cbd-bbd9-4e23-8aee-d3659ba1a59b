<template>
    <div>
        <span class="title"
            >请您对此访视前一周的情况进行评价。注意：您是否真的做过这些事情并不重要，关键是您是否能够做到这些项目。</span
        >
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-form-item label="1、穿衣与梳头" required> </el-form-item>
            <el-form-item
                label="1.1、能自己穿衣吗？包括系鞋带和扣扣子"
                prop="p1"
            >
                <el-radio-group v-model="form.p1">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="1.2、能自己洗头吗？" prop="p2">
                <el-radio-group v-model="form.p2">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="2、起身" required> </el-form-item>
            <el-form-item label="2.1、能从无扶手的直椅中直接站起吗？" prop="p3">
                <el-radio-group v-model="form.p3">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="2.2、能上床、起床吗？" prop="p4">
                <el-radio-group v-model="form.p4">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="3、吃饭" required> </el-form-item>
            <el-form-item label="3.1、能切肉吗？" prop="p5">
                <el-radio-group v-model="form.p5">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="3.2、能将装满水的玻璃杯送到嘴边吗？" prop="p6">
                <el-radio-group v-model="form.p6">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="3.3、能开启一盒未开封的牛奶吗？" prop="p7">
                <el-radio-group v-model="form.p7">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="4、行走" required> </el-form-item>
            <el-form-item label="4.1、能出门在平路上行走吗？" prop="p8">
                <el-radio-group v-model="form.p8">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="4.2、能上五个台阶吗？" prop="p9">
                <el-radio-group v-model="form.p9">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="5、个人卫生" required> </el-form-item>
            <el-form-item label="5.1、能洗澡并擦干吗？" prop="p10">
                <el-radio-group v-model="form.p10">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="5.2、能洗盆浴吗？" prop="p11">
                <el-radio-group v-model="form.p11">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="5.3、能自己上厕所吗？" prop="p12">
                <el-radio-group v-model="form.p12">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="6、触物" required> </el-form-item>
            <el-form-item
                label="6.1、能触到头顶上五斤重的物体并把它拿下来吗？"
                prop="p13"
            >
                <el-radio-group v-model="form.p13">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="6.2、能弯腰从地上拾起衣服吗？" prop="p14">
                <el-radio-group v-model="form.p14">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="7、握物" required> </el-form-item>
            <el-form-item
                label="7.1、能开小汽车（如出租车）车门吗？"
                prop="p15"
            >
                <el-radio-group v-model="form.p15">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="7.2、能打开已开启的罐头吗？" prop="p16">
                <el-radio-group v-model="form.p16">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="7.3、能关水龙头吗？" prop="p17">
                <el-radio-group v-model="form.p17">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="8、活动" required> </el-form-item>
            <el-form-item label="8.1、能跑腿或购物吗？" prop="p18">
                <el-radio-group v-model="form.p18">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="8.2、能上下小汽车（出租汽车）吗？" prop="p19">
                <el-radio-group v-model="form.p19">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="8.3、能做家务吗？如吸尘、收拾房间、简单的园艺"
                prop="p20"
            >
                <el-radio-group v-model="form.p20">
                    <el-radio label="毫无困难" border />
                    <el-radio label="稍有困难" border />
                    <el-radio label="很困难" border />
                    <el-radio label="无法完成" border />
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="HAQ评分总分："
                prop="p21"
                style="display: flex"
            >
                <el-input
                    v-model="form.p21"
                    disabled
                    style="width: 150px"
                ></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0041",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    watch: {
        form: {
            handler: function (newV) {
                if (newV) {
                    let rule = {
                        毫无困难: 0,
                        稍有困难: 1,
                        很困难: 2,
                        无法完成: 3,
                    };
                    let arr = Object.values(this.form)
                        .splice(0, 20)
                        .map((item) => {
                            return rule[item] || 0;
                        });
                    let max1 = Math.max(...[arr[0], arr[1]]);
                    let max2 = Math.max(...[arr[2], arr[3]]);
                    let max3 = Math.max(...[arr[4], arr[5], arr[6]]);
                    let max4 = Math.max(...[arr[7], arr[8]]);
                    let max5 = Math.max(...[arr[9], arr[10], arr[11]]);
                    let max6 = Math.max(...[arr[12], arr[13]]);
                    let max7 = Math.max(...[arr[14], arr[15], arr[16]]);
                    let max8 = Math.max(...[arr[17], arr[18], arr[19]]);
                    this.form.p21 =
                        (max1 +
                            max2 +
                            max3 +
                            max4 +
                            max5 +
                            max6 +
                            max7 +
                            max8) /
                        8;
                }
            },
            immediate: false,
            deep: true,
        },
    },
    data() {
        return {
            form: {
                p1: "",
                p2: "",
                p3: "",
                p4: "",
                p5: "",
                p6: "",
                p7: "",
                p8: "",
                p9: "",
                p10: "",
                p11: "",
                p12: "",
                p13: "",
                p14: "",
                p15: "",
                p16: "",
                p17: "",
                p18: "",
                p19: "",
                p20: "",
                p21: "",
            },
            // 表单校验
            rules: {},
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        calculate() {},
    },
};
</script>
<style scoped>
.title {
    font-weight: 600;
    color: grey;
}
</style>
