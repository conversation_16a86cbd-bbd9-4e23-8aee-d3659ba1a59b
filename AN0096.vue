<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="mini" label-position="top" label-width="80px" style="overflow-y: auto">
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p1" label="糖化血红蛋白">
            <el-input v-model="form.p1" @input="handleNumberInput($event, 'p1')" @blur="handleBlur('p1')">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0096',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '糖化血红蛋白必须输入', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleNumberInput(event, field) {
      let value = event.replace(/[^0-9.]/g, '');

      // 处理多个小数点的情况
      const decimalParts = value.split('.');
      if (decimalParts.length > 2) {
        value = decimalParts[0] + '.' + decimalParts.slice(1).join('');
      }

      // 限制整数部分为3位
      let max = 2;
      if (decimalParts[0].length > max) {
        value = decimalParts[0].substring(0, max) + (decimalParts[1] ? '.' + decimalParts[1] : '');
      }

      // 限制小数部分为2位
      if (decimalParts.length > 1 && decimalParts[1].length > 2) {
        value = decimalParts[0] + '.' + decimalParts[1].substring(0, 2);
      }
      this.form[field] = value;
    },
    handleBlur(field) {
      if (this.form[field] != undefined && this.form[field] != '') {
        this.form[field] = parseFloat(this.form[field]).toFixed(2);
      }
    },
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
