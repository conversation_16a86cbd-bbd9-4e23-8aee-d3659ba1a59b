<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-form-item label="受试者指出自己目前的疾病控制状态" prop="p1">
                <div class="process">
                    <div class="process-title">
                        <span>很好</span>
                        <span>很差</span>
                    </div>
                    <div class="process-box">
                        <div
                            v-for="(item, index) in 11"
                            :class="form.p1!=null&&form.p1 >= item-1 ? 'click-box' : ''"
                            :key="index"
                            @click="clickNode(item-1)"
                        >
                            {{ item - 1 }}
                        </div>
                    </div>
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0035",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p1: null,
            },
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: true,
                        message: "请选择疾病控制状态",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            this.validateData();
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        clickNode(index) {
            this.form.p1 = index;
        },
    },
};
</script>
<style scoped>
.process {
    width: 80%;
}
.process-title {
    display: flex;
    justify-content: space-between;
}

.process-box {
    display: flex;
    border: 1px solid #d7d7d7;
}
.process-box div {
    width: 10%;
    text-align: center;
    padding: 4px;
    cursor: pointer;
}
.click-box {
    background-color: #d7d7d7;
}
/deep/.el-form-item__label{
    font-weight: 450;
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
    color: #000000;
}
</style>
