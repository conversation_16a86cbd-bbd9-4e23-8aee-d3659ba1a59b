<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-form-item label="姓名：" style="display: flex" prop="p1">
                <el-input v-model="form.p1" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="性别：" style="display: flex" prop="p2">
                <el-radio-group v-model="form.p2">
                    <el-radio label="男" border>男</el-radio>
                    <el-radio label="女" border>女</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="1、最近一周稀便或水样便次数" required>
            </el-form-item>
            <el-form-item
                label="周一（次数）："
                prop="p3"
                style="display: flex; margin-left: 10px"
            >
                <el-input-number
                    v-model="form.p3"
                    :min="0"
                    :precision="0"
                    :step="1"
                    :controls="false"
                ></el-input-number>
            </el-form-item>
            <el-form-item
                label="周二（次数）："
                prop="p4"
                style="display: flex; margin-left: 10px"
            >
                <el-input-number
                    v-model="form.p4"
                    :min="0"
                    :precision="0"
                    :step="1"
                    :controls="false"
                ></el-input-number>
            </el-form-item>
            <el-form-item
                label="周三（次数）："
                prop="p5"
                style="display: flex; margin-left: 10px"
            >
                <el-input-number
                    v-model="form.p5"
                    :min="0"
                    :precision="0"
                    :step="1"
                    :controls="false"
                ></el-input-number>
            </el-form-item>
            <el-form-item
                label="周四（次数）："
                prop="p6"
                style="display: flex; margin-left: 10px"
            >
                <el-input-number
                    v-model="form.p6"
                    :min="0"
                    :precision="0"
                    :step="1"
                    :controls="false"
                ></el-input-number>
            </el-form-item>
            <el-form-item
                label="周五（次数）："
                prop="p7"
                style="display: flex; margin-left: 10px"
            >
                <el-input-number
                    v-model="form.p7"
                    :min="0"
                    :precision="0"
                    :step="1"
                    :controls="false"
                ></el-input-number>
            </el-form-item>
            <el-form-item
                label="周六（次数）："
                prop="p8"
                style="display: flex; margin-left: 10px"
            >
                <el-input-number
                    v-model="form.p8"
                    :min="0"
                    :precision="0"
                    :step="1"
                    :controls="false"
                ></el-input-number>
            </el-form-item>
            <el-form-item
                label="周日（次数）："
                prop="p9"
                style="display: flex; margin-left: 10px"
            >
                <el-input-number
                    v-model="form.p9"
                    :min="0"
                    :precision="0"
                    :step="1"
                    :controls="false"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="2、最近一周腹痛" required></el-form-item>
            <el-form-item
                label="周一："
                prop="p10"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p10">
                    <el-radio label="无" border>无</el-radio>
                    <el-radio label="轻" border>轻</el-radio>
                    <el-radio label="中" border>中</el-radio>
                    <el-radio label="重" border>重</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周二："
                prop="p11"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p11">
                    <el-radio label="无" border>无</el-radio>
                    <el-radio label="轻" border>轻</el-radio>
                    <el-radio label="中" border>中</el-radio>
                    <el-radio label="重" border>重</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周三："
                prop="p12"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p12">
                    <el-radio label="无" border>无</el-radio>
                    <el-radio label="轻" border>轻</el-radio>
                    <el-radio label="中" border>中</el-radio>
                    <el-radio label="重" border>重</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周四："
                prop="p13"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p13">
                    <el-radio label="无" border>无</el-radio>
                    <el-radio label="轻" border>轻</el-radio>
                    <el-radio label="中" border>中</el-radio>
                    <el-radio label="重" border>重</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周五："
                prop="p14"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p14">
                    <el-radio label="无" border>无</el-radio>
                    <el-radio label="轻" border>轻</el-radio>
                    <el-radio label="中" border>中</el-radio>
                    <el-radio label="重" border>重</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周六："
                prop="p15"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p15">
                    <el-radio label="无" border>无</el-radio>
                    <el-radio label="轻" border>轻</el-radio>
                    <el-radio label="中" border>中</el-radio>
                    <el-radio label="重" border>重</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周日："
                prop="p16"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p16">
                    <el-radio label="无" border>无</el-radio>
                    <el-radio label="轻" border>轻</el-radio>
                    <el-radio label="中" border>中</el-radio>
                    <el-radio label="重" border>重</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="3、最近一周全身状况" required></el-form-item>
            <el-form-item
                label="周一："
                prop="p17"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p17">
                    <el-radio label="好" border>好</el-radio>
                    <el-radio label="较差" border>较差</el-radio>
                    <el-radio label="差" border>差</el-radio>
                    <el-radio label="很差" border>很差</el-radio>
                    <el-radio label="极差" border>极差</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周二："
                prop="p18"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p18">
                    <el-radio label="好" border>好</el-radio>
                    <el-radio label="较差" border>较差</el-radio>
                    <el-radio label="差" border>差</el-radio>
                    <el-radio label="很差" border>很差</el-radio>
                    <el-radio label="极差" border>极差</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周三："
                prop="p19"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p19">
                    <el-radio label="好" border>好</el-radio>
                    <el-radio label="较差" border>较差</el-radio>
                    <el-radio label="差" border>差</el-radio>
                    <el-radio label="很差" border>很差</el-radio>
                    <el-radio label="极差" border>极差</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周四："
                prop="p20"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p20">
                    <el-radio label="好" border>好</el-radio>
                    <el-radio label="较差" border>较差</el-radio>
                    <el-radio label="差" border>差</el-radio>
                    <el-radio label="很差" border>很差</el-radio>
                    <el-radio label="极差" border>极差</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周五："
                prop="p21"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p21">
                    <el-radio label="好" border>好</el-radio>
                    <el-radio label="较差" border>较差</el-radio>
                    <el-radio label="差" border>差</el-radio>
                    <el-radio label="很差" border>很差</el-radio>
                    <el-radio label="极差" border>极差</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周六："
                prop="p22"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p22">
                    <el-radio label="好" border>好</el-radio>
                    <el-radio label="较差" border>较差</el-radio>
                    <el-radio label="差" border>差</el-radio>
                    <el-radio label="很差" border>很差</el-radio>
                    <el-radio label="极差" border>极差</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="周日："
                prop="p23"
                style="display: flex; margin-left: 10px"
            >
                <el-radio-group v-model="form.p23">
                    <el-radio label="好" border>好</el-radio>
                    <el-radio label="较差" border>较差</el-radio>
                    <el-radio label="差" border>差</el-radio>
                    <el-radio label="很差" border>很差</el-radio>
                    <el-radio label="极差" border>极差</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="4、是否有以下几种情况" prop="p24">
                <el-checkbox-group
                    v-model="form.p24"
                    style="display: flex; flex-direction: column; width: 400px"
                >
                    <el-checkbox
                        label="关节痛/关节炎"
                        border
                        style="margin-left: 10px"
                        class="mb-10"
                        >关节痛/关节炎</el-checkbox
                    >
                    <el-checkbox label="虹膜炎/色素膜炎" border class="mb-10"
                        >虹膜炎/色素膜炎</el-checkbox
                    >
                    <el-checkbox
                        label="结节红斑/坏疽性脓皮病/口腔性溃疡"
                        border
                        class="mb-10"
                        >结节红斑/坏疽性脓皮病/口腔性溃疡</el-checkbox
                    >
                    <el-checkbox label="肛裂/肛痿/肛周脓肿" border class="mb-10"
                        >肛裂/肛痿/肛周脓肿</el-checkbox
                    >
                    <el-checkbox label="其他痿管" border class="mb-10"
                        >其他痿管</el-checkbox
                    >
                    <el-checkbox
                        label="过去一周内体温>37.8℃"
                        border
                        style="margin-right: 30px"
                        >过去一周内体温>37.8℃</el-checkbox
                    >
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="5、需要服用茶已哌啶/阿片类止泻药" prop="p25">
                <el-radio-group v-model="form.p25">
                    <el-radio label="无" border>无</el-radio>
                    <el-radio label="有" border>有</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="6、是否有腹部包块" prop="p26">
                <el-radio-group v-model="form.p26">
                    <el-radio label="无" border>无</el-radio>
                    <el-radio label="可疑" border>可疑</el-radio>
                    <el-radio label="肯定" border>肯定</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="7、红细胞比容" prop="p27">
                <el-input-number
                    v-model="form.p27"
                    :precision="2"
                    :step="0.1"
                    :controls="false"
                >
                </el-input-number>
            </el-form-item>
            <el-form-item label="8.1、身高:" prop="p28" class="input-item">
                <el-input-number
                    class="input-unit"
                    :controls="false"
                    style="width: 120px"
                    v-model="form.p28"
                    :precision="0"
                    :step="1"
                    :max="300"
                    :min="20"
                ></el-input-number>
                <div class="unit">cm</div>
            </el-form-item>
            <el-form-item label="8.2、体重:" prop="p29" class="input-item">
                <el-input-number
                    class="input-unit"
                    :controls="false"
                    style="width: 120px"
                    v-model="form.p29"
                    :precision="2"
                    :max="650"
                    :min="0"
                ></el-input-number>
                <div class="unit">kg</div>
            </el-form-item>
            <el-form-item label="9、CDAI评分" prop="p30">
                <el-input
                    v-model="form.p30"
                    style="width: 150px"
                    readonly
                    disabled
                ></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0042",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    watch: {
        form: {
            handler: function (newV) {
                if (newV) {
                    let sum1 =
                        (newV.p3 +
                            newV.p4 +
                            newV.p5 +
                            newV.p6 +
                            newV.p7 +
                            newV.p8 +
                            newV.p9) *
                        2;
                    let sum2Rule = {
                        无: 0,
                        轻: 1,
                        中: 2,
                        重: 3,
                    };
                    let sum2 =
                        (sum2Rule[newV.p10] +
                            sum2Rule[newV.p11] +
                            sum2Rule[newV.p12] +
                            sum2Rule[newV.p13] +
                            sum2Rule[newV.p14] +
                            sum2Rule[newV.p15] +
                            sum2Rule[newV.p16]) *
                        5;
                    let sum3Rule = {
                        好: 0,
                        较差: 1,
                        差: 2,
                        很差: 3,
                        极差: 4,
                    };
                    let sum3 =
                        (sum3Rule[newV.p17] +
                            sum3Rule[newV.p18] +
                            sum3Rule[newV.p19] +
                            sum3Rule[newV.p20] +
                            sum3Rule[newV.p21] +
                            sum3Rule[newV.p22] +
                            sum3Rule[newV.p23]) *
                        7;
                    let sum4 = newV.p24.length * 20;
                    let sum5 = newV.p25 == "有" ? 30 : 0;
                    let sum6Rule = {
                        无: 0,
                        可疑: 2,
                        肯定: 5,
                    };
                    let sum6 = sum6Rule[newV.p26] * 10;
                    let sum7 = 0;
                    if (newV.p2 == "男") {
                        sum7 = (47 - newV.p27) * 6;
                    } else if (newV.p2 == "女") {
                        sum7 = (42 - newV.p27) * 6;
                    }
                    let sum8 = 0;
                    if (newV.p2 == "男") {
                        sum8 =
                            100 *
                            ((newV.p28 - 100 - newV.p29) / (newV.p28 - 100));
                    } else if (newV.p2 == "女") {
                        sum8 =
                            100 *
                            ((newV.p28 - 102 - newV.p29) / (newV.p28 - 102));
                    }
                    console.log(
                        sum1,
                        sum2,
                        sum3,
                        sum4,
                        sum5,
                        sum6,
                        sum7,
                        sum8,
                        "各项值"
                    );

                    let sum = (
                        sum1 +
                        sum2 +
                        sum3 +
                        sum4 +
                        sum5 +
                        sum6 +
                        sum7 +
                        sum8
                    ).toFixed(2);
                    this.form.p30 = sum !== "NaN" ? sum : 0;
                }
            },
            immediate: false,
            deep: true,
        },
    },
    data() {
        var checkP28 = (rule, value, callback) => {
            if (!value) {
                return callback(new Error("身高不能为空"));
            }
            if (!Number.isInteger(value)) {
                callback(new Error("请输入数字值"));
            } else {
                if (value < 20 || value > 300) {
                    callback(new Error("身高范围在20cm-300cm之间"));
                } else {
                    callback();
                }
            }
        };
        var checkP29 = (rule, value, callback) => {
            if ((value == "" && value != 0) || (value == null && value != 0)) {
                return callback(new Error("体重不能为空"));
            }
            console.log(value);

            if (Number.isNaN(Number(value))) {
                callback(new Error("请输入数字值"));
            } else {
                if (value < 0 || value > 650) {
                    callback(new Error("体重范围在0kg-650kg之间"));
                } else {
                    callback();
                }
            }
        };
        return {
            form: {
                p24: [],
            },
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "请输入姓名",
                        trigger: "blur",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "请选择性别",
                        trigger: "change",
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "请输入次数",
                        trigger: "blur",
                    },
                ],
                p4: [
                    {
                        required: true,
                        message: "请输入次数",
                        trigger: "blur",
                    },
                ],
                p5: [
                    {
                        required: true,
                        message: "请输入次数",
                        trigger: "blur",
                    },
                ],
                p6: [
                    {
                        required: true,
                        message: "请输入次数",
                        trigger: "blur",
                    },
                ],
                p7: [
                    {
                        required: true,
                        message: "请输入次数",
                        trigger: "blur",
                    },
                ],
                p8: [
                    {
                        required: true,
                        message: "请输入次数",
                        trigger: "blur",
                    },
                ],
                p9: [
                    {
                        required: true,
                        message: "请输入次数",
                        trigger: "blur",
                    },
                ],
                p10: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p11: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p12: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p13: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p14: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p15: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p16: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p17: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p18: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p19: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p20: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p21: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p22: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p23: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p25: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p26: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p27: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blur",
                    },
                ],
                p28: [
                    {
                        required: true,
                        message: "请输入",

                        trigger: "blur",
                    },
                ],
                p29: [
                    {
                        required: true,
                        message: "请输入",

                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
<style scoped>
.mb-10 {
    margin-bottom: 10px;
}
::v-deep .el-radio--mini.is-bordered .el-radio__inner {
    display: none !important;
}

::v-deep .el-radio {
    margin-right: 10px;
}

.input-item ::v-deep .el-form-item__content {
    display: flex;
}

.input-unit ::v-deep .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.unit {
    height: 26px;
    background-color: #f5f7fa;
    color: #909399;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    white-space: nowrap;
    width: 50px;
    text-align: center;
    font-size: 12px;
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
</style>
