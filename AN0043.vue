<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-form-item label="根据患者目前的表现，请问属于以下哪一种证型？" required> 
                <el-radio-group v-model="form.p0" @change="handleChange">
                    <el-radio v-for="(op, index) in options" :label="op.label" border style="margin-bottom: 10px;">{{ op.label }}</el-radio>
                    <el-radio label="不属上述于任一证型" border style="margin-bottom: 10px;">不属上述于任一证型</el-radio>
                </el-radio-group>
            </el-form-item>
            <template>
                <div v-for="(item, index) in options.filter(op => form.p0.includes(op.label))" :key="index">
                    <div style="font-size: 15px;font-weight: bold;color: #000;margin-bottom: 10px;">{{ item.label }}</div>
                    <el-form-item label="1、主症（多选）">
                        <el-checkbox-group v-model="form.p1">
                            <el-checkbox v-for="op in item.options1" :key="op.label" :label="op.label" border />
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="2、次症（多选）">
                        <el-checkbox-group v-model="form.p2">
                            <el-checkbox v-for="op in item.options3" :key="op.label" :label="op.label" border />
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="3、舌脉（多选）">
                        <el-checkbox-group v-model="form.p3">
                            <el-checkbox v-for="op in item.options2" :key="op.label" :label="op.label" border />
                        </el-checkbox-group>
                    </el-form-item>
                </div>
            </template>
            <!-- <el-form-item label="1、证型1:风湿痹阻证" required> </el-form-item>
            <el-form-item label="1.1、主症（多选）">
                <el-checkbox-group v-model="form.p1">
                    <el-checkbox label="关节疼痛、肿胀，游走不定" border />
                    <el-checkbox label="关节疼痛、肿胀，时发时止" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="1.2、次症（多选）">
                <el-checkbox-group v-model="form.p2">
                    <el-checkbox label="恶风，或汗出" border />
                    <el-checkbox label="头痛" border />
                    <el-checkbox label="肢体沉重" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="1.3、舌脉（多选）">
                <el-checkbox-group v-model="form.p3">
                    <el-checkbox label="舌质淡红，苔薄白" border />
                    <el-checkbox label="脉滑或浮" border />
                </el-checkbox-group>
            </el-form-item>

            <el-form-item label="2、证型2:寒湿痹阻证" required> </el-form-item>
            <el-form-item label="2.1、主症（多选）">
                <el-checkbox-group v-model="form.p4">
                    <el-checkbox label="关节冷痛，触之不温" border />
                    <el-checkbox label="疼痛遇寒加重，得热痛减" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="2.2、次症（多选）">
                <el-checkbox-group v-model="form.p5">
                    <el-checkbox label="关节拘急，屈伸不利" border />
                    <el-checkbox label="肢冷，或畏寒喜暖" border />
                    <el-checkbox label="口淡不渴" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="2.3、舌脉（多选）">
                <el-checkbox-group v-model="form.p6">
                    <el-checkbox label="舌体胖大，舌质淡，苔白或腻" border />
                    <el-checkbox label="脉弦或紧" border />
                </el-checkbox-group>
            </el-form-item>

            <el-form-item label="3、证型3:湿热痹阻证" required> </el-form-item>
            <el-form-item label="3.1、主症（多选）">
                <el-checkbox-group v-model="form.p7">
                    <el-checkbox label="关节肿热疼痛" border />
                    <el-checkbox label="关节触之热感或自觉热感" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="3.2、次症（多选）">
                <el-checkbox-group v-model="form.p8">
                    <el-checkbox label="关节局部皮色发红" border />
                    <el-checkbox label="发热" border />
                    <el-checkbox label="心烦" border />
                    <el-checkbox label="口渴或渴不欲饮" border />
                    <el-checkbox label="小便黄" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="3.3、舌脉（多选）">
                <el-checkbox-group v-model="form.p9">
                    <el-checkbox label="舌质红，苔黄腻或黄厚" border />
                    <el-checkbox label="脉弦滑或滑数" border />
                </el-checkbox-group>
            </el-form-item>

            <el-form-item label="4、证型4:痰瘀痹阻证" required> </el-form-item>
            <el-form-item label="4.1、主症（多选）">
                <el-checkbox-group v-model="form.p10">
                    <el-checkbox label="关节肿痛日久不消" border />
                    <el-checkbox
                        label="关节局部肤色晦暗，或有皮下结节"
                        border
                    />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="4.2、次症（多选）">
                <el-checkbox-group v-model="form.p11">
                    <el-checkbox label="关节肌肉刺痛" border />
                    <el-checkbox label="关节僵硬变形" border />
                    <el-checkbox label="面色黯黑" border />
                    <el-checkbox label="唇暗" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="4.3、舌脉（多选）">
                <el-checkbox-group v-model="form.p12">
                    <el-checkbox label="舌质紫暗或有瘀斑，苔腻" border />
                    <el-checkbox label="脉沉细涩或沉滑" border />
                </el-checkbox-group>
            </el-form-item>

            <el-form-item label="5、证型5:瘀血阻络证" required> </el-form-item>
            <el-form-item label="5.1、主症（多选）">
                <el-checkbox-group v-model="form.p13">
                    <el-checkbox label="关节刺痛，疼痛部位固定不移" border />
                    <el-checkbox label="疼痛夜甚" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="5.2、次症（多选）">
                <el-checkbox-group v-model="form.p14">
                    <el-checkbox label="肢体麻木" border />
                    <el-checkbox label="关节局部色略" border />
                    <el-checkbox label="肌肤甲错或干燥无泽" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="5.3、舌脉（多选）">
                <el-checkbox-group v-model="form.p15">
                    <el-checkbox
                        label="舌质紫暗，有瘀斑或瘀点，苔薄白"
                        border
                    />
                    <el-checkbox label="脉沉细涩" border />
                </el-checkbox-group>
            </el-form-item>

            <el-form-item label="6、证型6:气血两虚证" required> </el-form-item>
            <el-form-item label="6.1、主症（多选）">
                <el-checkbox-group v-model="form.p16">
                    <el-checkbox label="关节疼痛或隐痛，伴倦怠乏力" border />
                    <el-checkbox label="面色不华" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="6.2、次症（多选）">
                <el-checkbox-group v-model="form.p17">
                    <el-checkbox label="心悸气短" border />
                    <el-checkbox label="头晕" border />
                    <el-checkbox label="爪甲色淡" border />
                    <el-checkbox label="食少纳差" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="6.3、舌脉（多选）">
                <el-checkbox-group v-model="form.p18">
                    <el-checkbox label="舌质淡，苔薄" border />
                    <el-checkbox label="脉细弱或沉细无力" border />
                </el-checkbox-group>
            </el-form-item>

            <el-form-item label="7、证型7:肝肾不足证" required> </el-form-item>
            <el-form-item label="7.1、主症（多选）">
                <el-checkbox-group v-model="form.p19">
                    <el-checkbox label="关节疼痛，肿大或僵硬变形" border />
                    <el-checkbox label="腰膝酸软或腰背酸痛" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="7.2、次症（多选）">
                <el-checkbox-group v-model="form.p20">
                    <el-checkbox label="足跟痛" border />
                    <el-checkbox label="眩晕耳鸣" border />
                    <el-checkbox label="潮热盗汗" border />
                    <el-checkbox label="尿频，夜尿多" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="7.3、舌脉（多选）">
                <el-checkbox-group v-model="form.p21">
                    <el-checkbox label="舌质红，苔白或少苔" border />
                    <el-checkbox label="脉细数" border />
                </el-checkbox-group>
            </el-form-item>

            <el-form-item label="8、证型8:气阴两虚证" required> </el-form-item>
            <el-form-item label="8.1、主症（多选）">
                <el-checkbox-group v-model="form.p22">
                    <el-checkbox label="关节肿大伴气短乏力" border />
                    <el-checkbox label="肌肉酸痛，口干眼涩" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="8.2、次症（多选）">
                <el-checkbox-group v-model="form.p23">
                    <el-checkbox label="自汗或盗汗" border />
                    <el-checkbox label="手足心热" border />
                    <el-checkbox label="形体瘦弱，肌肤无泽" border />
                    <el-checkbox label="虚烦多梦" border />
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="8.3、舌脉（多选）">
                <el-checkbox-group v-model="form.p24">
                    <el-checkbox label="舌质红或有裂纹，苔少或无苔" border />
                    <el-checkbox label="脉沉细无力或细数无力" border />
                </el-checkbox-group>
            </el-form-item> -->
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0043",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p0: [],
                p1: [],
                p2: [],
                p3: [],
                // p4: [],
                // p5: [],
                // p6: [],
                // p7: [],
                // p8: [],
                // p9: [],
                // p10: [],
                // p11: [],
                // p12: [],
                // p13: [],
                // p14: [],
                // p15: [],
                // p16: [],
                // p17: [],
                // p18: [],
                // p19: [],
                // p20: [],
                // p21: [],
                // p22: [],
                // p23: [],
                // p24: [],
            },
            options: [
                { 
                    label: '风湿痹阻证', 
                    options1: [{label: '关节疼痛、肿胀，游走不定'},{label: '关节疼痛、肿胀，时发时止'},],
                    options2: [{label: '恶风，或汗出'},{label: '头痛'},{label: '肢体沉重'},],
                    options3: [{label: '舌质淡红，苔薄白'},{label: '脉滑或浮'},],
                },
                { 
                    label: '寒湿痹阻证', 
                    options1: [{label: '关节冷痛，触之不温'},{label: '疼痛遇寒加重，得热痛减'},],
                    options2: [{label: '关节拘急，屈伸不利'},{label: '肢冷，或畏寒喜暖'},{label: '口淡不渴'},],
                    options3: [{label: '舌体胖大，舌质淡，苔白或腻'},{label: '脉弦或紧'},],
                },
                { 
                    label: '湿热痹阻证', 
                    options1: [{label: '关节肿热疼痛'},{label: '关节触之热感或自觉热感'},],
                    options2: [{label: '关节局部皮色发红'},{label: '发热'},{label: '心烦'},{label: '口渴或渴不欲饮'},{label: '小便黄'}],
                    options3: [{label: '舌质红，苔黄腻或黄厚'},{label: '脉弦滑或滑数'},],
                },
                { 
                    label: '痰瘀痹阻证', 
                    options1: [{label: '关节肿痛日久不消'},{label: '关节局部肤色晦暗，或有皮下结节'},],
                    options2: [{label: '关节肌肉刺痛'},{label: '关节僵硬变形'},{label: '面色黯黑'},{label: '唇暗'}],
                    options3: [{label: '舌质紫暗或有瘀斑，苔腻'},{label: '脉沉细涩或沉滑'},],
                },
                { 
                    label: '瘀血阻络证', 
                    options1: [{label: '关节刺痛，疼痛部位固定不移'},{label: '疼痛夜甚'},],
                    options2: [{label: '肢体麻木'},{label: '关节局部色略'},{label: '肌肤甲错或干燥无泽'}],
                    options3: [{label: '舌质紫暗，有瘀斑或瘀点，苔薄白'},{label: '脉沉细涩'},],
                },
                { 
                    label: '气血两虚证', 
                    options1: [{label: '关节疼痛或隐痛，伴倦怠乏力'},{label: '面色不华'},],
                    options2: [{label: '心悸气短'},{label: '头晕'},{label: '爪甲色淡'},{label: '食少纳差'}],
                    options3: [{label: '舌质淡，苔薄'},{label: '脉细弱或沉细无力'},],
                },
                { 
                    label: '肝肾不足证', 
                    options1: [{label: '关节疼痛，肿大或僵硬变形'},{label: '腰膝酸软或腰背酸痛'},],
                    options2: [{label: '足跟痛'},{label: '眩晕耳鸣'},{label: '潮热盗汗'},{label: '尿频，夜尿多'}],
                    options3: [{label: '舌质红，苔白或少苔'},{label: '脉细数'},],
                },
                { 
                    label: '气阴两虚证', 
                    options1: [{label: '关节肿大伴气短乏力'},{label: '肌肉酸痛，口干眼涩'},],
                    options2: [{label: '自汗或盗汗'},{label: '手足心热'},{label: '形体瘦弱，肌肤无泽'},{label: '虚烦多梦'}],
                    options3: [{label: '舌质红或有裂纹，苔少或无苔'},{label: '脉沉细无力或细数无力'},],
                },
            ],
            // 表单校验
            rules: {},
        };
    },
    methods: {
        handleChange() {
            this.$set(this.form, "p1", []);
            this.$set(this.form, "p2", []);
            this.$set(this.form, "p3", []);
        },
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
