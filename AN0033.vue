<template>
    <div>
        <span class="title"
            >临床改善70％的ACR标准(ACR70)：要求肿胀及触痛关节数改善达70%，且下列5个参数中有3个改善达70%
            ①病人的整体评估；②医生的整体评估；③病人对疼痛程度评估（休息痛）；④日常生活能力（HAQ指数）；⑤急性期反应物的水平（ESR
            或 CRP）</span
        >
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-form-item label="1、肿胀关节数改善达到70%" prop="p1">
                <el-radio-group v-model="form.p1" class="flex-column">
                    <el-radio label="是" class="mb-10 mr-0" />
                    <el-radio label="否" />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="2、压痛关节数改善达到70%" prop="p2">
                <el-radio-group v-model="form.p2" class="flex-column">
                    <el-radio label="是" class="mb-10 mr-0" />
                    <el-radio label="否" />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="3、病人的整体评估改善达到70%" prop="p3">
                <el-radio-group v-model="form.p3" class="flex-column">
                    <el-radio label="是" class="mb-10 mr-0" />
                    <el-radio label="否" />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="4、医生的整体评估改善达到70%" prop="p4">
                <el-radio-group v-model="form.p4" class="flex-column">
                    <el-radio label="是" class="mb-10 mr-0" />
                    <el-radio label="否" />
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="5、病人对疼痛程度评估（休息痛）改善达到70%"
                prop="p5"
            >
                <el-radio-group v-model="form.p5" class="flex-column">
                    <el-radio label="是" class="mb-10 mr-0" />
                    <el-radio label="否" />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="6、HAQ评分改善达到70%" prop="p6">
                <el-radio-group v-model="form.p6" class="flex-column">
                    <el-radio label="是" class="mb-10 mr-0" />
                    <el-radio label="否" />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="7、ESR或CRP改善达到70%" prop="p7">
                <el-radio-group v-model="form.p7" class="flex-column">
                    <el-radio label="是" class="mb-10 mr-0" />
                    <el-radio label="否" />
                </el-radio-group>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0033",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {},
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p4: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p5: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],

                p6: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                p7: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
<style scoped>
.title {
    font-weight: 600;
    color: grey;
}
.flex-column {
    display: flex;
    flex-direction: column;
}
.mb-10 {
    margin-bottom: 10px;
}
.mr-0 {
    margin-right: 0px
}
</style>
