<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <el-row>
        <el-col :span="8">
          <el-form-item prop="p1" label="血压">
            <el-input v-model="form.p1" @input="handleNumberInput($event, 'p1')" placeholder="收缩压">
              <template slot="append">mmHg</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="1">
          <el-form-item prop="p1" label=" " label-width="0px" class="noRequiredIcon">
            <div style="text-align: center;">/</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="p2" label=" " label-width="0px" class="noRequiredIcon">
            <el-input v-model="form.p2" @input="handleNumberInput($event, 'p2')" placeholder="舒张压">
              <template slot="append">mmHg</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0089',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
        p2: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '收缩压必须输入', trigger: 'blur' }],
        p2: [{ required: true, message: '舒张压必须输入', trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleNumberInput(event, field) {
      // let value = event.replace(/[^0-9.]/g, '');

      // // 处理多个小数点的情况
      // const decimalParts = value.split('.');
      // if (decimalParts.length > 2) {
      //   value = decimalParts[0] + '.' + decimalParts.slice(1).join('');
      // }

      // // 限制整数部分为3位
      // if (decimalParts[0].length > 3) {
      //   value = decimalParts[0].substring(0, 3) + (decimalParts[1] ? '.' + decimalParts[1] : '');
      // }

      // // 限制小数部分为2位
      // if (decimalParts.length > 1 && decimalParts[1].length > 2) {
      //   value = decimalParts[0] + '.' + decimalParts[1].substring(0, 0);
      // }
    // 移除非数字字符
      let num = event.replace(/\D/g, '')
      
      // 限制长度为3位
      if (num.length > 3) {
        num = num.slice(0, 3)
      }
      if (num > 300) {
        num = 300
      }
      
      // 更新绑定值
      this.form[field] = num
    },
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
<style scoped>
/deep/ .noRequiredIcon .el-form-item__label:before {
  content: '' !important;
}
</style>
