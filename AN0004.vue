<template>
  <div>
    <el-form ref="form" :model="form"  :rules="rules" label-position="top" label-width="80px" style="overflow-y: auto">
        <el-row>
          <el-col>
            <el-form-item label="1、红细胞（高倍视野）" prop="p1">
              <el-input @input="handleInputP1"  v-model="form.p1" style="width: 250px"><template slot="append">HPF</template></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      <el-row>
        <el-col>
          <el-form-item label="2、白细胞（高倍视野）" prop="p2">
            <el-input @input="handleInputP2"  v-model="form.p2" style="width: 250px"><template slot="append">HPF</template></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-table v-if="this.formData!=null" :data="list" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column label="项目" align="center" prop="p1"/>
        <el-table-column label="结果" align="center" prop="p2"/>
        <el-table-column label="提示" align="center" prop="p3"/>
        <el-table-column label="单位" align="center" prop="p4"/>
        <el-table-column label="参考范围" align="center" prop="p5"/>
      </el-table>
    </el-form>
  </div>
</template>
<script>
  export default {
    name: "AN0004",
    props: {
      formData: String
    },
    components: {
    },
    created() {
      if(this.formData != null && this.formData!=''){
        this.form = JSON.parse(this.formData);
        this.handleInputP1(this.form.p1)
        this.handleInputP2(this.form.p2)
      }    },
    data() {
      return {
        form: {},
        list:[
          {"p1":"红细胞","p2":"","p3":"-","p4":"HFP","p5":"0-3"},
          {"p1":"白细胞","p2":"","p3":"-","p4":"HFP","p5":"0-3"}
         ],
        // 表单校验
        rules: {
          p1: [{ required: true, message: '红细胞（高倍视野）必须输入', trigger: 'blur' }],
          p2: [{ required: true, message: '白细胞（高倍视野）必须输入', trigger: 'blur' }]
        },
      };
    },
    methods: {
      /** 表单值 */
      getData(){
        return this.form;
      },
      validateData(){
        let rs = true
        this.$refs['form'].validate(valid => {
          if (valid) {
            rs = false;
          }
        });
        return rs;
      },
      handleInputP1(value){
        this.form.p1 = value.replace(/(\.\d{2})\d*/, '$1').slice(0,6);// 最多保留2位小数
        this.list[0].p2 = "无血尿";
        this.list[1].p2 = "无血尿";
        if(this.form.p1>3){
          this.list[0].p3 = "↑";
          this.list[0].p2 = "血尿";
          this.list[1].p2 = "血尿";
        } else if(this.form.p1<0){
          this.list[0].p3 = "↓";
        } else {
          this.list[0].p3 = "-";
        }
      },
      handleInputP2(value){
        this.form.p2 = value.replace(/(\.\d{2})\d*/, '$1').slice(0,6);// 最多保留2位小数
        if(this.form.p2>3){
          this.list[1].p3 = "↑";
        } else if(this.form.p1<0){
          this.list[1].p3 = "↓";
        } else {
          this.list[1].p3 = "-";
        }
      },
    }
  };
  export const formConfig = {
    fieldList:[
    {
      label: "1、红细胞（高倍视野）",
      key: "p1",
    },
    {
      label: "2、白细胞（高倍视野）",
      key: "p2",
    }
  ]}
</script>


<style scoped>
/* 自定义选中后的背景颜色 */
/deep/.el-radio.is-bordered.is-checked {
  background-color: springgreen !important; /* 设置选中后的背景色为红色 */
}

/* 自定义未选中的样式（可选） */
/deep/.el-radio.is-bordered {
  background-color: #fff; /* 默认背景色 */
  border-color: #dcdfe6; /* 默认边框颜色 */
}

/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px!important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none!important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item__label {
  text-align: right;
  vertical-align: middle;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>
