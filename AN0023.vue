<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="left"
            label-width="auto"
            size="mini"
        >
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="1、体温" prop="p1" class="inline-form-item">
                        <el-input-number
                            class="input-unit"
                            :controls="false"
                            style="width: 120px"
                            v-model="form.p1"
                            :precision="2"
                            :max="999.99"
                            :min="0"
                        ></el-input-number>
                        <div class="unit">℃</div>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="2、呼吸" prop="p2" class="inline-form-item">
                        <el-input-number
                            class="input-unit"
                            :controls="false"
                            style="width: 120px"
                            v-model="form.p2"
                            :precision="0"
                            :max="100"
                            :min="1"
                            :step="1"
                        ></el-input-number>

                        <div class="unit">次/分</div>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="3、心率" prop="p3" class="inline-form-item">
                        <el-input-number
                            class="input-unit"
                            :controls="false"
                            style="width: 120px"
                            v-model="form.p3"
                            :precision="0"
                            :max="300"
                            :min="1"
                            :step="1"
                        ></el-input-number>
                        <div class="unit">次/分</div>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="inline-flex">
                    <el-form-item label="4、血压" prop="p4" class="inline-form-item">
                        <el-input-number
                            :controls="false"
                            v-model="form.p4"
                            style="width: 120px"
                            :precision="0"
                            :max="300"
                            :min="1"
                            :step="1"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label-width="0">
                        <div style="margin: 0 5px;">/</div>
                    </el-form-item>
                    <el-form-item class="noneReqIcon inline-form-item" prop="p5" label="" label-width="0">
                        <el-input-number
                            class="input-unit"
                            :controls="false"
                            v-model="form.p5"
                            style="width: 120px"
                            :precision="0"
                            :max="300"
                            :min="1"
                            :step="1"
                        >
                        </el-input-number>

                        <div class="unit">mmHg</div>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0044",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p1: undefined,
                p2: undefined,
                p3: undefined,
                p4: undefined,
            },
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: true,
                        message: "请输入体温",
                        trigger: "blur",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "请输入呼吸",
                        trigger: "blur",
                    },
                ],
                p3: [
                    {
                        required: true,
                        message: "请输入心率",
                        trigger: "blur",
                    },
                ],
                p4: [
                    {
                        required: true,
                        message: "请输入血压",
                        trigger: "blur",
                    },
                ],
                p5: [
                    {
                        required: true,
                        message: "请输入血压",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>

<style scoped>

::v-deep .el-input__inner {
    text-align: center;
}
.inline-form-item ::v-deep(.el-form-item__content) {
    display: flex;
    align-items: center;
    flex-direction: row !important;
}

.input-unit ::v-deep .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.input-item ::v-deep .el-form-item__content {
    display: flex;
}

.noneReqIcon /deep/.el-form-item__label {
    display: none;
}
/deep/ .el-form-item__content.el-form-item__content {
    gap: 0px !important;
}
/deep/ .el-form-item.el-form-item {
    flex-direction: row !important;
    margin-bottom: 20px !important;
}
.inline-flex ::v-deep .el-form-item {
    width: auto !important;
}
.inline-flex /deep/.el-form-item__label-wrap {
    margin-left: 0px !important;
}
.unit {
    height: 28px;
    line-height: 28px;
    box-sizing: border-box;
    background-color: #f5f7fa;
    color: #909399;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    white-space: nowrap;
    width: 50px;
    text-align: center;
    font-size: 12px;
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
::v-deep .el-form-item__label {
    font-weight: 450;
    font-family: "PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC",
        sans-serif;
    color: #000000;
}
</style>
