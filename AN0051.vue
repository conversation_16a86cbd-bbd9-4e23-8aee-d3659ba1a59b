<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="mini" inline>
      <el-form-item v-for="item in questions" :key="item.prop" :prop="item.prop" :label="item.label">
        <el-radio-group v-model="form[item.prop]">
          <el-radio v-for="option in item.options" :key="option" :label="option" border />
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "AN0051",
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != "") {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      questions: [
        { label: "1、您的食欲（单选）", prop: "p1", options: ['很差', '差','一般','好','非常好']},
        { label: "2、您觉得食物的味道（单选）", prop: "p2", options: ['很差', '差','一般','好','非常好']},
        { label: "3、当您进餐时（单选）", prop: "p3", options: ['吃几口就觉得饱了', '吃不到一半就觉得饱了','吃一半就觉得饱了','吃完时觉得饱了','全部吃饭还没觉得饱']},
        { label: "4、通常您进餐（单选）", prop: "p4", options: ['一日不到一餐', '一日一餐','一日两餐','一日三餐','一日三餐以上']},
      ],
      form: {
        p1: undefined,
        p2: undefined,
        p3: undefined,
        p4: undefined,
      },
      // 表单校验
      rules: {
        p1: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p2: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p3: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        p4: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ]
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      console.log(this.form)
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>
<style scoped>
/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item {
  width: 100%;
}

/deep/.el-form-item__label {
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-form-item__content {
  width: 100%;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>
