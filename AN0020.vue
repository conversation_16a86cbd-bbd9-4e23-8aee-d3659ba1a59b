<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="left"
            size="mini"
        >
            <el-form-item label="肺HRCT检查结果:" prop="p1">
                <div style="display: flex; flex-direction: column">
                    <el-radio-group v-model="form.p1">
                        <el-radio label="正常" border>正常</el-radio>
                        <el-radio label="异常" border>异常</el-radio>
                    </el-radio-group>
                    <el-input
                        v-if="form.p1 == '异常'"
                        v-model="form.p2"
                        style="width: 400px; margin-top: 10px"
                        type="textarea"
                        maxlength="500"
                        show-word-limit
                    ></el-input>
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0020",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p1: "",
                p2: "",
            },
            // 表单校验
            rules: {
                // p1:绑定的名字
                p1: [
                    {
                        required: true,
                        message: "请选择检查结果",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
