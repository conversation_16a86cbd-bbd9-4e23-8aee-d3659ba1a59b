<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            label-width="80px"
            style="overflow-y: auto"
            size="mini"
        >
            <el-form-item label="退出研究原因（受试者可因下列任一原因退出研究）" prop="p1">
                <el-radio-group
                    v-model="form.p1"
                    style="
                        display: flex;
                        flex-direction: column;
                        overflow: hidden;
                    "
                >
                    <el-radio
                        style="margin-left: 10px"
                        class="mb-10"
                        label="受试者要求退出(收回/撤销知情同意)"
                        border
                        >受试者要求退出(收回/撤销知情同意)</el-radio
                    >
                    <el-radio label="使用禁止使用的药物用于治疗" border class="mb-10"
                        >使用禁止使用的药物用于治疗</el-radio
                    >
                    <el-radio label="任何病理性事件、临床不良事件或受试者身体情况出现变化，导致医师认为继续参加研究并不符合患者的最佳利益" border class="mb-10"
                        >任何病理性事件、临床不良事件或受试者身体情况出现变化，导致医师认为继续参加研究并不符合患者的最佳利益</el-radio
                    >
                    <el-radio
                        label="违反试验方案"
                        border
                        class="mb-10"
                        >违反试验方案</el-radio
                    >
                    <el-radio
                        label="受试者死亡"
                        class="mb-10"
                        border
                        >受试者死亡</el-radio
                    >

                    <el-radio
                        label="其他导致退出研究的原因（需要详细记录）"
                        class="mb-10"
                        border
                        >其他导致退出研究的原因（需要详细记录）</el-radio
                    >
                </el-radio-group>
            </el-form-item>

            <el-form-item v-if="form.p1 === '其他导致退出研究的原因（需要详细记录）'" label="其他原因" prop="p2">
                <el-input
                    v-model="form.p2"
                    style="width: 600px"
                    type="textarea"
                    maxlength="1000"
                    show-word-limit
                    :rows="4"
                ></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0085",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {},
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "退出研究原因必须选择",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
    },
};
</script>
<style scoped>
.mb-10 {
    margin-bottom: 10px;
}

/deep/.el-radio {
    margin-right: 0px !important;
}
</style>
