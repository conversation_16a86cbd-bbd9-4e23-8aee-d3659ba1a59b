<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <!-- 1、位置 -->
      <el-form-item prop="location" label="1、位置">
        <el-checkbox-group v-model="form.location">
          <el-checkbox label="外周带" border />
          <el-checkbox label="移行带" border />
          <el-checkbox label="中央带" border />
          <el-checkbox label="尿道周围腺体" border />
          <el-checkbox label="其他" border />
        </el-checkbox-group>
        <el-input
          v-if="form.location.includes('其他')"
          v-model="form.locationOther"
          placeholder="请填写其他位置"
          maxlength="50"
          show-word-limit
          class="location-other"
        />
      </el-form-item>

      <!-- 2、数量 -->
      <el-form-item prop="number" label="2、数量">
        <el-input
          v-model="form.number"
          placeholder="请输入数量"
          style="width: 200px"
          @input="handleNumberInput"
          @keypress="handleIntegerKeypress"
        />
      </el-form-item>

      <!-- 3、大小 -->
      <el-form-item prop="sizeType" label="3、大小">
        <el-radio-group v-model="form.sizeType" class="size-type-group">
          <el-radio label="单个" border />
          <el-radio label="多个" border />
        </el-radio-group>
      </el-form-item>

      <!-- 大小输入 -->
      <el-form-item v-if="form.sizeType" prop="sizeValue" label="">
        <!-- 单个病灶的三维尺寸 -->
        <div v-if="form.sizeType === '单个'" class="size-single">
          <div class="size-input-group">
            <el-input
              v-model="form.sizeMaxDiameter"
              placeholder="最大径"
              style="width: 120px"
              @input="handleSizeInput('sizeMaxDiameter', $event)"
              @keypress="handleNumberKeypress"
            />
            <span style="margin: 0 5px;">×</span>
            <el-input
              v-model="form.sizeVerticalDiameter"
              placeholder="垂直径"
              style="width: 120px"
              @input="handleSizeInput('sizeVerticalDiameter', $event)"
              @keypress="handleNumberKeypress"
            />
            <span style="margin: 0 5px;">×</span>
            <el-input
              v-model="form.sizeAnteriorPosteriorDiameter"
              placeholder="前后径"
              style="width: 120px"
              @input="handleSizeInput('sizeAnteriorPosteriorDiameter', $event)"
              @keypress="handleNumberKeypress"
            />
            <span style="margin-left: 5px;">mm³</span>
          </div>
        </div>
        <!-- 多个病灶的最大径 -->
        <div v-if="form.sizeType === '多个'" class="size-multiple">
          <el-input
            v-model="form.sizeMultipleMaxDiameter"
            placeholder="病灶最大径"
            style="width: 200px"
            @input="handleSizeInput('sizeMultipleMaxDiameter', $event)"
            @keypress="handleNumberKeypress"
          />
          <span style="margin-left: 5px;">mm</span>
        </div>
      </el-form-item>

      <!-- 4、外观 -->
      <el-form-item label="4、外观">
        <!-- 生长形态 -->
        <el-form-item prop="growthPattern" label="（1）生长形态">
          <el-radio-group v-model="form.growthPattern" class="growth-pattern-group">
            <el-radio label="腺泡型生长" border />
            <el-radio label="筛状生长" border />
            <el-radio label="实性生长" border />
            <el-radio label="条索状生长" border />
            <el-radio label="其他" border />
          </el-radio-group>
          <el-input
            v-if="form.growthPattern === '其他'"
            v-model="form.growthPatternOther"
            placeholder="请填写其他生长形态"
            maxlength="50"
            show-word-limit
            class="growth-pattern-other"
          />
        </el-form-item>

        <!-- 颜色 -->
        <el-form-item prop="color" label="（2）颜色">
          <el-radio-group v-model="form.color" class="color-group">
            <el-radio label="灰白色" border />
            <el-radio label="灰黄色" border />
            <el-radio label="暗红色" border />
            <el-radio label="灰黑色" border />
            <el-radio label="其他" border />
          </el-radio-group>
          <el-input
            v-if="form.color === '其他'"
            v-model="form.colorOther"
            placeholder="请填写其他颜色"
            maxlength="50"
            show-word-limit
            class="color-other"
          />
        </el-form-item>

        <!-- 质地 -->
        <el-form-item prop="texture" label="（3）质地">
          <el-radio-group v-model="form.texture" class="texture-group">
            <el-radio label="软" border />
            <el-radio label="硬" border />
            <el-radio label="沙砾感" border />
            <el-radio label="其他" border />
          </el-radio-group>
          <el-input
            v-if="form.texture === '其他'"
            v-model="form.textureOther"
            placeholder="请填写其他质地"
            maxlength="50"
            show-word-limit
            class="texture-other"
          />
        </el-form-item>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0164',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
      // 初始化所有可能的字段
      const fields = [
        'location', 'locationOther', 'number', 'sizeType', 'sizeMaxDiameter', 'sizeVerticalDiameter',
        'sizeAnteriorPosteriorDiameter', 'sizeMultipleMaxDiameter', 'growthPattern', 'growthPatternOther',
        'color', 'colorOther', 'texture', 'textureOther'
      ];

      fields.forEach(field => {
        if (this.form[field] === undefined) {
          if (field === 'location') {
            this.$set(this.form, field, []);
          } else {
            this.$set(this.form, field, '');
          }
        }
      });
    }
  },
  data() {
    // 验证函数
    const validateLocation = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error('请选择位置'));
      } else {
        callback();
      }
    };

    const validateLocationOther = (rule, value, callback) => {
      if (this.form.location.includes('其他') && !value) {
        callback(new Error('请填写其他位置'));
      } else {
        callback();
      }
    };

    const validateNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入数量'));
      } else if (!/^\d+$/.test(value)) {
        callback(new Error('请输入有效的整数'));
      } else if (parseInt(value) <= 0) {
        callback(new Error('数量必须大于0'));
      } else {
        callback();
      }
    };

    const validateSizeType = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择大小类型'));
      } else {
        callback();
      }
    };

    const validateSizeValue = (rule, value, callback) => {
      if (this.form.sizeType === '单个') {
        if (!this.form.sizeMaxDiameter || !this.form.sizeVerticalDiameter || !this.form.sizeAnteriorPosteriorDiameter) {
          callback(new Error('请输入完整的三维尺寸'));
        } else {
          callback();
        }
      } else if (this.form.sizeType === '多个') {
        if (!this.form.sizeMultipleMaxDiameter) {
          callback(new Error('请输入病灶最大径'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };

    const validateGrowthPattern = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择生长形态'));
      } else {
        callback();
      }
    };

    const validateGrowthPatternOther = (rule, value, callback) => {
      if (this.form.growthPattern === '其他' && !value) {
        callback(new Error('请填写其他生长形态'));
      } else {
        callback();
      }
    };

    const validateColor = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择颜色'));
      } else {
        callback();
      }
    };

    const validateColorOther = (rule, value, callback) => {
      if (this.form.color === '其他' && !value) {
        callback(new Error('请填写其他颜色'));
      } else {
        callback();
      }
    };

    const validateTexture = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择质地'));
      } else {
        callback();
      }
    };

    const validateTextureOther = (rule, value, callback) => {
      if (this.form.texture === '其他' && !value) {
        callback(new Error('请填写其他质地'));
      } else {
        callback();
      }
    };

    return {
      form: {
        location: [],
        locationOther: '',
        number: '',
        sizeType: '',
        sizeMaxDiameter: '',
        sizeVerticalDiameter: '',
        sizeAnteriorPosteriorDiameter: '',
        sizeMultipleMaxDiameter: '',
        growthPattern: '',
        growthPatternOther: '',
        color: '',
        colorOther: '',
        texture: '',
        textureOther: '',
      },
      // 表单校验
      rules: {
        location: [{ validator: validateLocation, trigger: 'change' }],
        locationOther: [{ validator: validateLocationOther, trigger: 'blur' }],
        number: [{ validator: validateNumber, trigger: 'blur' }],
        sizeType: [{ validator: validateSizeType, trigger: 'change' }],
        sizeValue: [{ validator: validateSizeValue, trigger: 'blur' }],
        growthPattern: [{ validator: validateGrowthPattern, trigger: 'change' }],
        growthPatternOther: [{ validator: validateGrowthPatternOther, trigger: 'blur' }],
        color: [{ validator: validateColor, trigger: 'change' }],
        colorOther: [{ validator: validateColorOther, trigger: 'blur' }],
        texture: [{ validator: validateTexture, trigger: 'change' }],
        textureOther: [{ validator: validateTextureOther, trigger: 'blur' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
    /** 处理尺寸输入，只允许数字和小数点 */
    handleSizeInput(field, value) {
      // 只保留数字和小数点
      const numericValue = value.replace(/[^\d.]/g, '');
      // 确保只有一个小数点
      const parts = numericValue.split('.');
      if (parts.length > 2) {
        this.form[field] = parts[0] + '.' + parts.slice(1).join('');
      } else {
        this.form[field] = numericValue;
      }
    },
    /** 处理数量输入，只允许整数 */
    handleNumberInput(value) {
      // 只保留数字
      const numericValue = value.replace(/[^\d]/g, '');
      this.form.number = numericValue;
    },
    /** 处理按键事件，只允许整数 */
    handleIntegerKeypress(event) {
      const charCode = event.which ? event.which : event.keyCode;
      // 只允许数字(48-57)、退格(8)、删除(46)、方向键等控制键
      if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        event.preventDefault();
      }
    },
    /** 处理按键事件，只允许数字、小数点、退格和删除键 */
    handleNumberKeypress(event) {
      const charCode = event.which ? event.which : event.keyCode;
      // 允许数字(48-57)、小数点(46)、退格(8)、删除(46)、方向键等控制键
      if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
        event.preventDefault();
      }
      // 如果已经有小数点，不允许再输入小数点
      if (charCode === 46 && event.target.value.indexOf('.') !== -1) {
        event.preventDefault();
      }
    },
  },
  watch: {
    'form.location'(val) {
      if (!val.includes('其他')) {
        this.form.locationOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('locationOther');
        }
      }
    },
    'form.sizeType'(val) {
      // 当大小类型选择改变时，清空相关的尺寸数据
      if (val === '单个') {
        this.form.sizeMultipleMaxDiameter = '';
      } else if (val === '多个') {
        this.form.sizeMaxDiameter = '';
        this.form.sizeVerticalDiameter = '';
        this.form.sizeAnteriorPosteriorDiameter = '';
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate('sizeValue');
      }
    },
    'form.growthPattern'(val) {
      if (val !== '其他') {
        this.form.growthPatternOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('growthPatternOther');
        }
      }
    },
    'form.color'(val) {
      if (val !== '其他') {
        this.form.colorOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('colorOther');
        }
      }
    },
    'form.texture'(val) {
      if (val !== '其他') {
        this.form.textureOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('textureOther');
        }
      }
    },
  },
};
export const formConfig = {
  fieldList: [ {
      key:"location",
      label:"1、位置"
    }, {
      key:"number",
      label:"2、数量"
    }, {
      key:"sizeType",
      label:"3、大小"
    }, {
      key:"growthPattern",
      label:"4、外观 - 生长形态"
    }, {
      key:"color",
      label:"4、外观 - 颜色"
    }, {
      key:"texture",
      label:"4、外观 - 质地"
    } ],
};
</script>
<style scoped>
::v-deep .el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

::v-deep .el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-radio__label {
  padding-left: 0px;
}

::v-deep .el-checkbox--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
  margin-right: 10px;
  margin-bottom: 10px;
}

::v-deep .el-checkbox__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-checkbox__label {
  padding-left: 0px;
}

::v-deep .el-form-item__label {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

::v-deep .el-radio-group {
  font-size: 0;
}

::v-deep .el-checkbox-group {
  font-size: 0;
}

::v-deep .el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

::v-deep .el-checkbox {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

.location-other,
.growth-pattern-other,
.color-other,
.texture-other {
  width: 300px;
  margin-left: 10px;
  margin-top: 10px;
}

.size-type-group,
.growth-pattern-group,
.color-group,
.texture-group {
  display: inline-flex;
  align-items: center;
}

.size-input-group {
  display: flex;
  align-items: center;
}

.size-single,
.size-multiple {
  margin-top: 10px;
}
</style>
