<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini" style="overflow-y: auto">
      <el-form-item prop="p1" label="1、您每周是否进行至少两次的体育锻炼？（单选）">
        <el-radio-group v-model="form.p1">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p2" label="2、您每天的饮食是否包含至少400克的蔬菜或水果？（单选）">
        <el-radio-group v-model="form.p2">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p3" label="3、您是否目前吸烟或使用其他烟草制品？（单选）">
        <el-radio-group v-model="form.p3">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p4" label="4、您是否饮酒？（单选）">
        <el-radio-group v-model="form.p4">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0090',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
    }
  },
  data() {
    return {
      form: {
        p1: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '每周是否进行至少两次的体育锻炼必须选择', trigger: 'blur' }],
        p2: [{ required: true, message: '每天的饮食是否包含至少400克的蔬菜或水果必须选择', trigger: 'blur' }],
        p3: [{ required: true, message: '是否目前吸烟或使用其他烟草制品必须选择', trigger: 'blur' }],
        p4: [{ required: true, message: '是否饮酒必须选择', trigger: 'blur' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
};
</script>

<style scoped>
/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item__label {
  /* text-align: right; */
  vertical-align: middle;
  /* float: left; */
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>
