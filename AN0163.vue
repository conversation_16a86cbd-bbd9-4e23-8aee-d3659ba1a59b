<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <!-- 1、基本信息 -->
      <el-form-item prop="surgeryDate" label="1、手术日期">
        <el-date-picker
          v-model="form.surgeryDate"
          type="date"
          placeholder="选择手术日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item prop="surgeryNumber" label="手术编号">
        <el-input
          v-model="form.surgeryNumber"
          placeholder="请输入手术编号"
          maxlength="50"
          show-word-limit
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item prop="surgeryDepartment" label="手术科室">
        <el-input
          v-model="form.surgeryDepartment"
          placeholder="请输入手术科室"
          maxlength="50"
          show-word-limit
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item prop="mainSurgeon" label="主刀医师">
        <el-input
          v-model="form.mainSurgeon"
          placeholder="请输入主刀医师"
          maxlength="50"
          show-word-limit
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item prop="assistantSurgeon" label="助手医师">
        <el-input
          v-model="form.assistantSurgeon"
          placeholder="请输入助手医师"
          maxlength="100"
          show-word-limit
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item prop="anesthesiologist" label="麻醉医师">
        <el-input
          v-model="form.anesthesiologist"
          placeholder="请输入麻醉医师"
          maxlength="50"
          show-word-limit
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item prop="nurseTeam" label="护士团队">
        <el-input
          v-model="form.nurseTeam"
          placeholder="请输入护士团队"
          maxlength="100"
          show-word-limit
          style="width: 300px"
        />
      </el-form-item>

      <!-- 2、手术方式 -->
      <el-form-item prop="surgeryType" label="2、手术方式">
        <el-radio-group v-model="form.surgeryType">
          <el-radio label="腹腔镜前列腺癌根治术" border/>
          <el-radio label="达芬奇辅助前列腺癌根治术" border/>
          <el-radio label="开放前列腺癌根治术" border/>
          <el-radio label="经尿道前列腺等离子切除术" border/>
        </el-radio-group>
      </el-form-item>

      <!-- 腹腔镜前列腺癌根治术 -->
      <div v-if="form.surgeryType === '腹腔镜前列腺癌根治术'">
        <el-form-item prop="laparoscopicLymphClearance" label="盆腔淋巴结清扫">
          <el-radio-group v-model="form.laparoscopicLymphClearance">
            <el-radio label="有" border/>
            <el-radio label="无" border/>
          </el-radio-group>
        </el-form-item>

        <div v-if="form.laparoscopicLymphClearance === '有'">
          <el-form-item prop="laparoscopicClearanceRange" label="清扫范围（可多选）">
            <el-checkbox-group v-model="form.laparoscopicClearanceRange">
              <el-checkbox label="髂总淋巴结" border />
              <el-checkbox label="髂内淋巴结" border />
              <el-checkbox label="髂外淋巴结" border />
              <el-checkbox label="闭孔淋巴结" border />
              <el-checkbox label="骶前淋巴结" border />
            </el-checkbox-group>
          </el-form-item>

          <el-form-item prop="laparoscopicLymphNumber" label="清扫淋巴结数量">
            <el-input
              v-model="form.laparoscopicLymphNumber"
              placeholder="请输入数量"
              style="width: 200px"
              @input="handleIntegerInput('laparoscopicLymphNumber', $event)"
              @keypress="handleIntegerKeypress"
            />
            <span style="margin-left: 5px;">枚</span>
          </el-form-item>

          <el-form-item prop="laparoscopicCompleteClearance" label="是否完整清扫">
            <el-radio-group v-model="form.laparoscopicCompleteClearance">
              <el-radio label="是" border/>
              <el-radio label="否" border/>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>

      <!-- 达芬奇辅助前列腺癌根治术 -->
      <div v-if="form.surgeryType === '达芬奇辅助前列腺癌根治术'">
        <el-form-item prop="davinciLymphClearance" label="盆腔淋巴结清扫">
          <el-radio-group v-model="form.davinciLymphClearance">
            <el-radio label="有" border/>
            <el-radio label="无" border/>
          </el-radio-group>
        </el-form-item>

        <div v-if="form.davinciLymphClearance === '有'">
          <el-form-item prop="davinciClearanceRange" label="清扫范围（可多选）">
            <el-checkbox-group v-model="form.davinciClearanceRange">
              <el-checkbox label="髂总淋巴结" border />
              <el-checkbox label="髂内淋巴结" border />
              <el-checkbox label="髂外淋巴结" border />
              <el-checkbox label="闭孔淋巴结" border />
              <el-checkbox label="骶前淋巴结" border />
            </el-checkbox-group>
          </el-form-item>

          <el-form-item prop="davinciLymphNumber" label="清扫淋巴结数量">
            <el-input
              v-model="form.davinciLymphNumber"
              placeholder="请输入数量"
              style="width: 200px"
              @input="handleIntegerInput('davinciLymphNumber', $event)"
              @keypress="handleIntegerKeypress"
            />
            <span style="margin-left: 5px;">枚</span>
          </el-form-item>

          <el-form-item prop="davinciCompleteClearance" label="是否完整清扫">
            <el-radio-group v-model="form.davinciCompleteClearance">
              <el-radio label="是" border/>
              <el-radio label="否" border/>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>

      <!-- 开放前列腺癌根治术 -->
      <div v-if="form.surgeryType === '开放前列腺癌根治术'">
        <el-form-item prop="openLymphClearance" label="盆腔淋巴结清扫">
          <el-radio-group v-model="form.openLymphClearance">
            <el-radio label="有" border/>
            <el-radio label="无" border/>
          </el-radio-group>
        </el-form-item>

        <div v-if="form.openLymphClearance === '有'">
          <el-form-item prop="openClearanceRange" label="清扫范围（可多选）">
            <el-checkbox-group v-model="form.openClearanceRange">
              <el-checkbox label="髂总淋巴结" border />
              <el-checkbox label="髂内淋巴结" border />
              <el-checkbox label="髂外淋巴结" border />
              <el-checkbox label="闭孔淋巴结" border />
              <el-checkbox label="骶前淋巴结" border />
            </el-checkbox-group>
          </el-form-item>

          <el-form-item prop="openLymphNumber" label="清扫淋巴结数量">
            <el-input
              v-model="form.openLymphNumber"
              placeholder="请输入数量"
              style="width: 200px"
              @input="handleIntegerInput('openLymphNumber', $event)"
              @keypress="handleIntegerKeypress"
            />
            <span style="margin-left: 5px;">枚</span>
          </el-form-item>

          <el-form-item prop="openCompleteClearance" label="是否完整清扫">
            <el-radio-group v-model="form.openCompleteClearance">
              <el-radio label="是" border/>
              <el-radio label="否" border/>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>

      <!-- 经尿道前列腺等离子切除术 -->
      <div v-if="form.surgeryType === '经尿道前列腺等离子切除术'">
        <el-form-item prop="anesthesiaType" label="麻醉类型">
          <el-radio-group v-model="form.anesthesiaType">
            <el-radio label="全身麻醉" border/>
            <el-radio label="腰麻" border/>
            <el-radio label="硬膜外麻醉" border/>
            <el-radio label="局部麻醉" border/>
          </el-radio-group>
        </el-form-item>

        <el-form-item prop="bloodLoss" label="术中出血量">
          <el-input
            v-model="form.bloodLoss"
            placeholder="请输入出血量"
            style="width: 200px"
            @input="handleNumberInput('bloodLoss', $event)"
            @keypress="handleNumberKeypress"
          />
          <span style="margin-left: 5px;">ml</span>
        </el-form-item>

        <el-form-item prop="surgeryDuration" label="手术时长">
          <el-input
            v-model="form.surgeryDuration"
            placeholder="请输入手术时长"
            style="width: 200px"
            @input="handleNumberInput('surgeryDuration', $event)"
            @keypress="handleNumberKeypress"
          />
          <span style="margin-left: 5px;">分钟</span>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0163',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
      // 初始化所有可能的字段
      const fields = [
        'laparoscopicClearanceRange', 'davinciClearanceRange', 'openClearanceRange'
      ];

      fields.forEach(field => {
        if (this.form[field] === undefined) {
          this.$set(this.form, field, []);
        }
      });
    }
  },
  data() {
    // 验证函数
    const validateSurgeryDate = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择手术日期'));
      } else {
        callback();
      }
    };

    const validateSurgeryNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手术编号'));
      } else {
        callback();
      }
    };

    const validateSurgeryDepartment = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手术科室'));
      } else {
        callback();
      }
    };

    const validateMainSurgeon = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入主刀医师'));
      } else {
        callback();
      }
    };

    const validateSurgeryType = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择手术方式'));
      } else {
        callback();
      }
    };

    return {
      form: {
        surgeryDate: '',
        surgeryNumber: '',
        surgeryDepartment: '',
        mainSurgeon: '',
        assistantSurgeon: '',
        anesthesiologist: '',
        nurseTeam: '',
        surgeryType: '',
        // 腹腔镜相关
        laparoscopicLymphClearance: '',
        laparoscopicClearanceRange: [],
        laparoscopicLymphNumber: '',
        laparoscopicCompleteClearance: '',
        // 达芬奇相关
        davinciLymphClearance: '',
        davinciClearanceRange: [],
        davinciLymphNumber: '',
        davinciCompleteClearance: '',
        // 开放手术相关
        openLymphClearance: '',
        openClearanceRange: [],
        openLymphNumber: '',
        openCompleteClearance: '',
        // 经尿道手术相关
        anesthesiaType: '',
        bloodLoss: '',
        surgeryDuration: '',
      },
      // 表单校验
      rules: {
        surgeryDate: [{ validator: validateSurgeryDate, trigger: 'change' }],
        surgeryNumber: [{ validator: validateSurgeryNumber, trigger: 'blur' }],
        surgeryDepartment: [{ validator: validateSurgeryDepartment, trigger: 'blur' }],
        mainSurgeon: [{ validator: validateMainSurgeon, trigger: 'blur' }],
        surgeryType: [{ validator: validateSurgeryType, trigger: 'change' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
    /** 处理整数输入 */
    handleIntegerInput(field, value) {
      const numericValue = value.replace(/[^\d]/g, '');
      this.form[field] = numericValue;
    },
    /** 处理数字输入（包含小数） */
    handleNumberInput(field, value) {
      const numericValue = value.replace(/[^\d.]/g, '');
      const parts = numericValue.split('.');
      if (parts.length > 2) {
        this.form[field] = parts[0] + '.' + parts.slice(1).join('');
      } else {
        this.form[field] = numericValue;
      }
    },
    /** 处理按键事件，只允许整数 */
    handleIntegerKeypress(event) {
      const charCode = event.which ? event.which : event.keyCode;
      if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        event.preventDefault();
      }
    },
    /** 处理按键事件，允许数字和小数点 */
    handleNumberKeypress(event) {
      const charCode = event.which ? event.which : event.keyCode;
      if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
        event.preventDefault();
      }
      if (charCode === 46 && event.target.value.indexOf('.') !== -1) {
        event.preventDefault();
      }
    },
  },
  watch: {
    'form.surgeryType'(val) {
      // 清空其他手术方式的相关字段
      if (val !== '腹腔镜前列腺癌根治术') {
        this.form.laparoscopicLymphClearance = '';
        this.form.laparoscopicClearanceRange = [];
        this.form.laparoscopicLymphNumber = '';
        this.form.laparoscopicCompleteClearance = '';
      }
      if (val !== '达芬奇辅助前列腺癌根治术') {
        this.form.davinciLymphClearance = '';
        this.form.davinciClearanceRange = [];
        this.form.davinciLymphNumber = '';
        this.form.davinciCompleteClearance = '';
      }
      if (val !== '开放前列腺癌根治术') {
        this.form.openLymphClearance = '';
        this.form.openClearanceRange = [];
        this.form.openLymphNumber = '';
        this.form.openCompleteClearance = '';
      }
      if (val !== '经尿道前列腺等离子切除术') {
        this.form.anesthesiaType = '';
        this.form.bloodLoss = '';
        this.form.surgeryDuration = '';
      }
    },
    'form.laparoscopicLymphClearance'(val) {
      if (val !== '有') {
        this.form.laparoscopicClearanceRange = [];
        this.form.laparoscopicLymphNumber = '';
        this.form.laparoscopicCompleteClearance = '';
      }
    },
    'form.davinciLymphClearance'(val) {
      if (val !== '有') {
        this.form.davinciClearanceRange = [];
        this.form.davinciLymphNumber = '';
        this.form.davinciCompleteClearance = '';
      }
    },
    'form.openLymphClearance'(val) {
      if (val !== '有') {
        this.form.openClearanceRange = [];
        this.form.openLymphNumber = '';
        this.form.openCompleteClearance = '';
      }
    },
  },
};
export const formConfig = {
  fieldList: [ {
      key:"surgeryDate",
      label:"手术日期"
    }, {
      key:"surgeryNumber",
      label:"手术编号"
    }, {
      key:"surgeryDepartment",
      label:"手术科室"
    }, {
      key:"mainSurgeon",
      label:"主刀医师"
    }, {
      key:"surgeryType",
      label:"手术方式"
    } ],
};
</script>
<style scoped>
::v-deep .el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

::v-deep .el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-radio__label {
  padding-left: 0px;
}

::v-deep .el-checkbox--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
  margin-right: 10px;
  margin-bottom: 10px;
}

::v-deep .el-checkbox__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-checkbox__label {
  padding-left: 0px;
}

::v-deep .el-form-item__label {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

::v-deep .el-radio-group {
  font-size: 0;
}

::v-deep .el-checkbox-group {
  font-size: 0;
}

::v-deep .el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

::v-deep .el-checkbox {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>
